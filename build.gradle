// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    repositories {
        // 1. 腾讯云镜像（无需登录）
        maven{ url 'https://mirrors.cloud.tencent.com/nexus/repository/maven-public/' }
        maven{ url 'https://mirrors.cloud.tencent.com/repository/maven/' }
        
        // 2. 华为云镜像
        maven{ url 'https://repo.huaweicloud.com/repository/maven/' }
        
        // 3. 阿里云公共仓库（无需登录）
        maven{ url 'https://maven.aliyun.com/nexus/content/groups/public/' }
        maven{ url 'https://maven.aliyun.com/nexus/content/repositories/google/' }
        maven{ url 'https://maven.aliyun.com/nexus/content/repositories/gradle-plugin/' }
        
        // 4. 官方源作为最后备用
        google()
        mavenCentral()
        gradlePluginPortal()
    }
    dependencies {
        // Updated AGP version to match Gradle 8.5
        classpath 'com.android.tools.build:gradle:8.2.2'

        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        // 1. 腾讯云镜像（无需登录）
        maven{ url 'https://mirrors.cloud.tencent.com/nexus/repository/maven-public/' }
        maven{ url 'https://mirrors.cloud.tencent.com/repository/maven/' }
        
        // 2. 华为云镜像
        maven{ url 'https://repo.huaweicloud.com/repository/maven/' }
        
        // 3. 阿里云公共仓库（无需登录）
        maven{ url 'https://maven.aliyun.com/nexus/content/groups/public/' }
        maven{ url 'https://maven.aliyun.com/nexus/content/repositories/google/' }
        
        // 4. 官方源作为最后备用
        google()
        mavenCentral()
    }
}

tasks.register('clean', Delete) {
    delete rootProject.layout.buildDirectory
}