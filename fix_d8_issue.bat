@echo off
echo 正在修复D8编译问题...
echo.

echo 1. 清理项目缓存...
call gradlew clean
if errorlevel 1 (
    echo 清理失败，请检查Gradle配置
    pause
    exit /b 1
)

echo.
echo 2. 清理Gradle缓存...
call gradlew cleanBuildCache
if errorlevel 1 (
    echo 清理Gradle缓存失败
)

echo.
echo 3. 删除.gradle文件夹...
if exist .gradle (
    rmdir /s /q .gradle
    echo .gradle文件夹已删除
)

echo.
echo 4. 删除Gradle wrapper缓存...
if exist "%USERPROFILE%\.gradle\wrapper\dists" (
    rmdir /s /q "%USERPROFILE%\.gradle\wrapper\dists"
    echo Gradle wrapper缓存已删除
)

echo.
echo 5. 删除各模块的build文件夹...
for /d %%i in (com.android.*) do (
    if exist "%%i\build" (
        rmdir /s /q "%%i\build"
        echo 已删除 %%i\build
    )
)

echo.
echo 6. 重新编译项目...
call gradlew assembleDebug --stacktrace --info
if errorlevel 1 (
    echo 编译失败，请查看错误信息
    pause
    exit /b 1
) else (
    echo 编译成功！
)

echo.
echo 修复完成！
pause