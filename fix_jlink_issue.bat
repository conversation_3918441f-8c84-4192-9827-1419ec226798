@echo off
echo 正在修复 jlink.exe 相关的构建问题...
echo.

echo 1. 停止所有 Gradle 守护进程...
call gradlew --stop
if errorlevel 1 (
    echo 停止 Gradle 守护进程失败，继续执行...
)

echo.
echo 2. 清理项目缓存...
call gradlew clean
if errorlevel 1 (
    echo 清理失败，请检查Gradle配置
    pause
    exit /b 1
)

echo.
echo 3. 删除 .gradle 文件夹...
if exist .gradle (
    rmdir /s /q .gradle
    echo .gradle文件夹已删除
)

echo.
echo 4. 删除用户 Gradle 缓存...
if exist "%USERPROFILE%\.gradle\caches" (
    rmdir /s /q "%USERPROFILE%\.gradle\caches"
    echo 用户 Gradle 缓存已删除
)

echo.
echo 5. 删除各模块的 build 文件夹...
for /d %%i in (com.android.*) do (
    if exist "%%i\build" (
        rmdir /s /q "%%i\build"
        echo 已删除 %%i\build
    )
)

echo.
echo 6. 清理 Android SDK 缓存（如果存在）...
if exist "%ANDROID_HOME%\build-cache" (
    rmdir /s /q "%ANDROID_HOME%\build-cache"
    echo Android SDK 构建缓存已删除
)

echo.
echo 7. 重新同步项目依赖...
call gradlew --refresh-dependencies
if errorlevel 1 (
    echo 依赖同步失败，继续执行...
)

echo.
echo 8. 尝试构建项目（禁用配置缓存）...
call gradlew assembleDebug --no-configuration-cache --stacktrace
if errorlevel 1 (
    echo 构建失败，请查看错误信息
    echo.
    echo 如果仍然出现 jlink.exe 错误，请尝试：
    echo 1. 以管理员身份运行 Android Studio
    echo 2. 检查 Android Studio 安装路径是否包含特殊字符
    echo 3. 重新安装 Android SDK Platform 34
    pause
    exit /b 1
) else (
    echo 构建成功！
)

echo.
echo 修复完成！如果构建成功，可以重新启用配置缓存。
pause
