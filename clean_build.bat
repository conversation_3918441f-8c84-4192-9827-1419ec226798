@echo off
echo 正在清理Android项目构建缓存...

echo 1. 停止Gradle守护进程...
call gradlew --stop

echo 2. 清理项目构建文件...
call gradlew clean

echo 3. 删除.gradle缓存目录...
if exist ".gradle" (
    rmdir /s /q ".gradle"
    echo .gradle目录已删除
)

echo 4. 删除各模块的build目录...
for /d %%i in (com.android.*) do (
    if exist "%%i\build" (
        rmdir /s /q "%%i\build"
        echo 已删除 %%i\build
    )
)

echo 5. 删除根目录build文件夹...
if exist "build" (
    rmdir /s /q "build"
    echo build目录已删除
)

echo 清理完成！现在可以重新构建项目。
echo 建议在Android Studio中执行: File > Invalidate Caches and Restart
pause