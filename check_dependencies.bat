@echo off
echo 检查Android项目依赖关系
echo ========================

echo 检查 com.android.dialer 模块依赖...
call gradlew :com.android.dialer:dependencies --configuration debugRuntimeClasspath > dialer_dependencies.txt
echo 依赖信息已保存到 dialer_dependencies.txt

echo.
echo 检查 com.android.contacts.common 模块依赖...
call gradlew :com.android.contacts.common:dependencies --configuration debugRuntimeClasspath > contacts_common_dependencies.txt
echo 依赖信息已保存到 contacts_common_dependencies.txt

echo.
echo 检查依赖冲突...
call gradlew :com.android.dialer:dependencyInsight --dependency com.google.guava --configuration debugRuntimeClasspath
echo.
call gradlew :com.android.dialer:dependencyInsight --dependency libphonenumber --configuration debugRuntimeClasspath

echo.
echo 依赖检查完成！请查看生成的txt文件了解详细信息。
pause