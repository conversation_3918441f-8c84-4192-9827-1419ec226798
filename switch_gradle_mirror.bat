@echo off
echo Gradle镜像切换工具
echo.

echo 请选择要使用的Gradle镜像：
echo 1. 腾讯云镜像（推荐）
echo 2. 华为云镜像
echo 3. 官方源
echo 4. 查看当前配置
echo.

set /p choice=请输入选择 (1-4): 

if "%choice%"=="1" (
    echo 切换到腾讯云镜像...
    powershell -Command "(Get-Content gradle\wrapper\gradle-wrapper.properties) -replace 'distributionUrl=.*', 'distributionUrl=https\\://mirrors.cloud.tencent.com/gradle/gradle-8.1.1-bin.zip' | Set-Content gradle\wrapper\gradle-wrapper.properties"
    echo 已切换到腾讯云镜像
) else if "%choice%"=="2" (
    echo 切换到华为云镜像...
    powershell -Command "(Get-Content gradle\wrapper\gradle-wrapper.properties) -replace 'distributionUrl=.*', 'distributionUrl=https\\://repo.huaweicloud.com/gradle/gradle-8.1.1-bin.zip' | Set-Content gradle\wrapper\gradle-wrapper.properties"
    echo 已切换到华为云镜像
) else if "%choice%"=="3" (
    echo 切换到官方源...
    powershell -Command "(Get-Content gradle\wrapper\gradle-wrapper.properties) -replace 'distributionUrl=.*', 'distributionUrl=https\\://services.gradle.org/distributions/gradle-8.1.1-bin.zip' | Set-Content gradle\wrapper\gradle-wrapper.properties"
    echo 已切换到官方源
) else if "%choice%"=="4" (
    echo 当前Gradle配置：
    type gradle\wrapper\gradle-wrapper.properties | findstr distributionUrl
) else (
    echo 无效选择
)

echo.
echo 当前配置：
type gradle\wrapper\gradle-wrapper.properties | findstr distributionUrl

echo.
echo 如需重新下载Gradle，请运行: force_gradle_download.bat
pause