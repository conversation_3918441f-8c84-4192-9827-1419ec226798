#!/usr/bin/env pwsh

# Fragment API Migration Script
# This script helps migrate from old android.app.Fragment to androidx.fragment.app.Fragment

Write-Host "Starting Fragment API migration..." -ForegroundColor Green

# Define the replacements
$replacements = @{
    "import android.app.Fragment;" = "import androidx.fragment.app.Fragment;"
    "import android.app.FragmentManager;" = "import androidx.fragment.app.FragmentManager;"
    "import android.app.FragmentTransaction;" = "import androidx.fragment.app.FragmentTransaction;"
    "import android.app.LoaderManager;" = "import androidx.loader.app.LoaderManager;"
    "import android.content.Loader;" = "import androidx.loader.content.Loader;"
    "import android.content.CursorLoader;" = "import androidx.loader.content.CursorLoader;"
    "import android.app.FragmentPagerAdapter;" = "import androidx.fragment.app.FragmentPagerAdapter;"
    "import android.app.FragmentStatePagerAdapter;" = "import androidx.fragment.app.FragmentStatePagerAdapter;"
    "extends Fragment" = "extends Fragment"
    "FragmentManager" = "FragmentManager"
    "FragmentTransaction" = "FragmentTransaction"
    "LoaderManager" = "LoaderManager"
    "getLoaderManager\(\)" = "LoaderManager.getInstance(this)"
    "getFragmentManager\(\)" = "getParentFragmentManager()"
    "getChildFragmentManager\(\)" = "getChildFragmentManager()"
}

# Find all Java files in the dialer module
$javaFiles = Get-ChildItem -Path "com.android.dialer\src" -Filter "*.java" -Recurse

Write-Host "Found $($javaFiles.Count) Java files to process" -ForegroundColor Yellow

foreach ($file in $javaFiles) {
    $content = Get-Content $file.FullName -Raw
    $originalContent = $content
    $modified = $false
    
    # Apply replacements
    foreach ($old in $replacements.Keys) {
        $new = $replacements[$old]
        if ($content -match $old) {
            $content = $content -replace $old, $new
            $modified = $true
        }
    }
    
    # Write back if modified
    if ($modified) {
        Set-Content -Path $file.FullName -Value $content -NoNewline
        Write-Host "Updated: $($file.Name)" -ForegroundColor Cyan
    }
}

Write-Host "Fragment API migration completed!" -ForegroundColor Green
Write-Host "Note: You may need to manually review and fix some method calls and dependencies." -ForegroundColor Yellow