# Project-wide Gradle settings.

# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.

# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html

# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
# Increased memory allocation to prevent OOM errors during DEX processing
org.gradle.jvmargs=-Xmx2048m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8 -XX:+UseG1GC

# Use JDK 17 for better compatibility with newer Android projects
# org.gradle.java.home=F:\\Program Files\\Java\\jdk-17
# Comment out to use Android Studio's embedded JDK

# AndroidX support
android.useAndroidX=true
android.enableJetifier=true

# Suppress unsupported compileSdk warning
android.suppressUnsupportedCompileSdk=34

# Skip lint checks for faster build
android.lint.checkReleaseBuilds=false

# Enable parallel builds and configuration cache for better performance
org.gradle.parallel=true
org.gradle.caching=true
# Temporarily disable configuration cache to fix jlink.exe issues
# org.gradle.configuration-cache=true
org.gradle.configuration-cache=false

# Disable unnecessary features to speed up build - removed deprecated R8 options

# Fix for DEX processing issues - removed deprecated options

# Additional fixes for compilation issues
android.defaults.buildfeatures.buildconfig=true
android.nonTransitiveRClass=false
android.nonFinalResIds=false