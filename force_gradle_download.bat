@echo off
echo 强制重新下载Gradle...
echo.

echo 1. 删除本地Gradle wrapper缓存...
if exist "%USERPROFILE%\.gradle\wrapper" (
    rmdir /s /q "%USERPROFILE%\.gradle\wrapper"
    echo 本地Gradle wrapper缓存已删除
)

echo.
echo 2. 删除项目.gradle文件夹...
if exist .gradle (
    rmdir /s /q .gradle
    echo 项目.gradle文件夹已删除
)

echo.
echo 3. 验证Gradle配置...
echo 当前Gradle下载地址:
type gradle\wrapper\gradle-wrapper.properties | findstr distributionUrl
echo.
echo 如果下载失败，请查看 gradle-wrapper-alternatives.txt 文件获取备用镜像地址

echo.
echo 4. 强制重新下载Gradle...
call gradlew --version
if errorlevel 1 (
    echo Gradle下载失败，请检查网络连接
    pause
    exit /b 1
) else (
    echo Gradle下载成功！
)

echo.
echo 5. 显示Gradle信息...
call gradlew --version

echo.
echo 完成！现在可以运行 fix_d8_issue.bat 进行编译
pause