# Gradle 8.1.1 镜像地址备选方案
# 如果当前镜像无法下载，可以替换 gradle/wrapper/gradle-wrapper.properties 中的 distributionUrl

# 1. 腾讯云镜像（推荐，无需登录）
distributionUrl=https\://mirrors.cloud.tencent.com/gradle/gradle-8.1.1-bin.zip

# 2. 华为云镜像
distributionUrl=https\://repo.huaweicloud.com/gradle/gradle-8.1.1-bin.zip

# 3. 官方源（可能较慢）
distributionUrl=https\://services.gradle.org/distributions/gradle-8.1.1-bin.zip

# 4. 如果以上都不行，可以尝试其他版本
distributionUrl=https\://mirrors.cloud.tencent.com/gradle/gradle-8.0-bin.zip
distributionUrl=https\://mirrors.cloud.tencent.com/gradle/gradle-7.6-bin.zip

# 使用方法：
# 1. 复制上面任意一行 distributionUrl
# 2. 替换 gradle/wrapper/gradle-wrapper.properties 文件中的对应行
# 3. 删除 .gradle 文件夹
# 4. 重新运行 gradlew 命令