#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 1042284544 bytes for G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3548), pid=15276, tid=14960
#
# JRE version: Java(TM) SE Runtime Environment (17.0.11+7) (build 17.0.11+7-LTS-207)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (17.0.11+7-LTS-207, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: -XX:+HeapDumpOnOutOfMemoryError -XX:+UseG1GC --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx4096m -Dfile.encoding=UTF-8 -Duser.country=CN -Duser.language=zh -Duser.variant -javaagent:H:\Users\zhang\.gradle\wrapper\dists\gradle-8.5-bin\5hry6tgzq0wontdz18qo6fdj9\gradle-8.5\lib\agents\gradle-instrumentation-agent-8.5.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.5

Host: AMD Ryzen 5 5500GT with Radeon Graphics        , 12 cores, 31G,  Windows 11 , 64 bit Build 26100 (10.0.26100.3323)
Time: Sun Jul 27 16:34:25 2025  Windows 11 , 64 bit Build 26100 (10.0.26100.3323) elapsed time: 252.115922 seconds (0d 0h 4m 12s)

---------------  T H R E A D  ---------------

Current thread (0x000001a53844dee0):  VMThread "VM Thread" [stack: 0x0000006242400000,0x0000006242500000] [id=14960]

Stack: [0x0000006242400000,0x0000006242500000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x679cca]
V  [jvm.dll+0x7da13d]
V  [jvm.dll+0x7dba83]
V  [jvm.dll+0x7dc0f3]
V  [jvm.dll+0x2449af]
V  [jvm.dll+0x676ce9]
V  [jvm.dll+0x66b852]
V  [jvm.dll+0x3018d6]
V  [jvm.dll+0x308e76]
V  [jvm.dll+0x3596ee]
V  [jvm.dll+0x35991f]
V  [jvm.dll+0x2d89a8]
V  [jvm.dll+0x2d6ddd]
V  [jvm.dll+0x2d642c]
V  [jvm.dll+0x319f8b]
V  [jvm.dll+0x7e09eb]
V  [jvm.dll+0x7e1724]
V  [jvm.dll+0x7e1c3d]
V  [jvm.dll+0x7e2014]
V  [jvm.dll+0x7e20e0]
V  [jvm.dll+0x78a85a]
V  [jvm.dll+0x678bb5]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0xbbf6c]

VM_Operation (0x00000062477fd640): G1CollectForAllocation, mode: safepoint, requested by thread 0x000001a50292ef10


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001a505704510, length=226, elements={
0x000001a514e33630, 0x000001a538451c60, 0x000001a538452730, 0x000001a538468df0,
0x000001a53846b7d0, 0x000001a53846c0a0, 0x000001a53846c970, 0x000001a538471640,
0x000001a538472000, 0x000001a538472910, 0x000001a53843c3e0, 0x000001a57a2b7040,
0x000001a50079de60, 0x000001a500112110, 0x000001a500889830, 0x000001a57f4ba1f0,
0x000001a57f4b88a0, 0x000001a57f4b8db0, 0x000001a57f4b97d0, 0x000001a57f4bac10,
0x000001a5002d1b60, 0x000001a5002d43e0, 0x000001a5002d2fa0, 0x000001a5002d48f0,
0x000001a5002d80a0, 0x000001a57f4b8390, 0x000001a57a3ef800, 0x000001a501681440,
0x000001a5016832a0, 0x000001a5016837b0, 0x000001a501683cc0, 0x000001a501680a20,
0x000001a501681950, 0x000001a501680f30, 0x000001a502935960, 0x000001a50292f930,
0x000001a50292f420, 0x000001a502bdd300, 0x000001a502bdc3d0, 0x000001a502bdd810,
0x000001a502bdc8e0, 0x000001a502bddd20, 0x000001a502bda570, 0x000001a502bde230,
0x000001a502bdaa80, 0x000001a502bde740, 0x000001a502bdec50, 0x000001a502bdb4a0,
0x000001a502bdf160, 0x000001a502bdf670, 0x000001a502bdfb80, 0x000001a502be0ab0,
0x000001a502be05a0, 0x000001a502be0fc0, 0x000001a502be19e0, 0x000001a57a3ec050,
0x000001a57a3efd10, 0x000001a57a3ee3c0, 0x000001a57a3ee8d0, 0x000001a57a3ef2f0,
0x000001a57a3edeb0, 0x000001a57a3f0220, 0x000001a57a3ed9a0, 0x000001a57a3f0730,
0x000001a57a3ecf80, 0x000001a57a3f1150, 0x000001a57a3f1660, 0x000001a57a3f2590,
0x000001a57a3f2fb0, 0x000001a57a3f34c0, 0x000001a57a3f39d0, 0x000001a57a3f1b70,
0x000001a57a3ed490, 0x000001a57a3f2080, 0x000001a502930d70, 0x000001a5029326c0,
0x000001a502931280, 0x000001a50292ef10, 0x000001a502934a30, 0x000001a502932bd0,
0x000001a502934520, 0x000001a502934f40, 0x000001a5029330e0, 0x000001a5029335f0,
0x000001a502931790, 0x000001a502931ca0, 0x000001a502936380, 0x000001a502936890,
0x000001a5002d4e00, 0x000001a5002d6c60, 0x000001a5002d6750, 0x000001a5002d34b0,
0x000001a5002d39c0, 0x000001a5002d7680, 0x000001a5002d8ac0, 0x000001a57f4bb630,
0x000001a57f4b92c0, 0x000001a57f4b9ce0, 0x000001a57f4ba700, 0x000001a57f4bb120,
0x000001a57f4bbb40, 0x000001a5016841d0, 0x000001a501682370, 0x000001a501682880,
0x000001a501682d90, 0x000001a5029321b0, 0x000001a502934010, 0x000001a502930860,
0x000001a57a3f2aa0, 0x000001a502538d50, 0x000001a502536ef0, 0x000001a502537400,
0x000001a502538330, 0x000001a50253d430, 0x000001a50253ca10, 0x000001a50253d940,
0x000001a50253c500, 0x000001a50253cf20, 0x000001a50253a190, 0x000001a50253a6a0,
0x000001a50253b0c0, 0x000001a50253b5d0, 0x000001a5002d2580, 0x000001a5038c6d00,
0x000001a5038c67f0, 0x000001a5038c8650, 0x000001a5038c9fa0, 0x000001a5038c9a90,
0x000001a5038c8b60, 0x000001a5038c9580, 0x000001a5038c7210, 0x000001a50253bae0,
0x000001a502539770, 0x000001a50292fe40, 0x000001a5068c3ca0, 0x000001a5068c4bd0,
0x000001a5068c2860, 0x000001a5068c3280, 0x000001a5068c50e0, 0x000001a5068c2d70,
0x000001a5068c41b0, 0x000001a5068c1930, 0x000001a5068c1e40, 0x000001a5068c6a30,
0x000001a5068c6010, 0x000001a5068c3790, 0x000001a5068c6520, 0x000001a5068c55f0,
0x000001a5068c6f40, 0x000001a5068c7450, 0x000001a5068c7960, 0x000001a5068c7e70,
0x000001a5068c46c0, 0x000001a5068c8380, 0x000001a5068c8890, 0x000001a5068c8da0,
0x000001a5038c9070, 0x000001a5068c92b0, 0x000001a503ea6330, 0x000001a503ea3fc0,
0x000001a503ea44d0, 0x000001a503ea6840, 0x000001a503ea5400, 0x000001a503ea5e20,
0x000001a503ea5910, 0x000001a503ea3ab0, 0x000001a503ea3090, 0x000001a503ea7260,
0x000001a503ea7c80, 0x000001a503ea7770, 0x000001a503ea6d50, 0x000001a503ea8190,
0x000001a503ea49e0, 0x000001a503ea86a0, 0x000001a503ea4ef0, 0x000001a503ea90c0,
0x000001a503ea8bb0, 0x000001a503ea95d0, 0x000001a503ea9ae0, 0x000001a503ea9ff0,
0x000001a503eaa500, 0x000001a503eaaa10, 0x000001a5038c7c30, 0x000001a5027f9590,
0x000001a5027fa9d0, 0x000001a5027f7220, 0x000001a5027f7c40, 0x000001a5027f9aa0,
0x000001a5027faee0, 0x000001a5027f8150, 0x000001a5027f8b70, 0x000001a5027f9080,
0x000001a5027fd250, 0x000001a5027fd760, 0x000001a5027fc320, 0x000001a5027fdc70,
0x000001a5027fe180, 0x000001a5027ff0b0, 0x000001a5027fe690, 0x000001a5027fbe10,
0x000001a5027feba0, 0x000001a5027fc830, 0x000001a5027ff5c0, 0x000001a5027ffad0,
0x000001a5027fffe0, 0x000001a5028004f0, 0x000001a5027fcd40, 0x000001a502803280,
0x000001a502803790, 0x000001a502803ca0, 0x000001a5027f9fb0, 0x000001a502800f10,
0x000001a502802350, 0x000001a502801420, 0x000001a502801e40, 0x000001a5028055f0,
0x000001a502805b00, 0x000001a502806010, 0x000001a502806520, 0x000001a502802d70,
0x000001a502806a30, 0x000001a5002d5d30, 0x000001a50623dbd0, 0x000001a50447c420,
0x000001a506f0aed0, 0x000001a50e85f070
}

Java Threads: ( => current thread )
  0x000001a514e33630 JavaThread "main" [_thread_blocked, id=36208, stack(0x0000006241e00000,0x0000006241f00000)]
  0x000001a538451c60 JavaThread "Reference Handler" daemon [_thread_blocked, id=9320, stack(0x0000006242500000,0x0000006242600000)]
  0x000001a538452730 JavaThread "Finalizer" daemon [_thread_blocked, id=17160, stack(0x0000006242600000,0x0000006242700000)]
  0x000001a538468df0 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=28540, stack(0x0000006242700000,0x0000006242800000)]
  0x000001a53846b7d0 JavaThread "Attach Listener" daemon [_thread_blocked, id=34160, stack(0x0000006242800000,0x0000006242900000)]
  0x000001a53846c0a0 JavaThread "Service Thread" daemon [_thread_blocked, id=39308, stack(0x0000006242900000,0x0000006242a00000)]
  0x000001a53846c970 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=24704, stack(0x0000006242a00000,0x0000006242b00000)]
  0x000001a538471640 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=15404, stack(0x0000006242b00000,0x0000006242c00000)]
  0x000001a538472000 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=16644, stack(0x0000006242c00000,0x0000006242d00000)]
  0x000001a538472910 JavaThread "Sweeper thread" daemon [_thread_blocked, id=42820, stack(0x0000006242d00000,0x0000006242e00000)]
  0x000001a53843c3e0 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=38892, stack(0x0000006242e00000,0x0000006242f00000)]
  0x000001a57a2b7040 JavaThread "Notification Thread" daemon [_thread_blocked, id=6628, stack(0x0000006242f00000,0x0000006243000000)]
  0x000001a50079de60 JavaThread "Daemon health stats" [_thread_blocked, id=42692, stack(0x0000006243c00000,0x0000006243d00000)]
  0x000001a500112110 JavaThread "Incoming local TCP Connector on port 53799" [_thread_in_native, id=9568, stack(0x0000006243d00000,0x0000006243e00000)]
  0x000001a500889830 JavaThread "Daemon periodic checks" [_thread_blocked, id=5912, stack(0x0000006243e00000,0x0000006243f00000)]
  0x000001a57f4ba1f0 JavaThread "Cache worker for journal cache (H:\Users\zhang\.gradle\caches\journal-1)" [_thread_blocked, id=25464, stack(0x0000006244600000,0x0000006244700000)]
  0x000001a57f4b88a0 JavaThread "File lock request listener" [_thread_in_native, id=38116, stack(0x0000006244700000,0x0000006244800000)]
  0x000001a57f4b8db0 JavaThread "Cache worker for file hash cache (H:\Users\zhang\.gradle\caches\8.5\fileHashes)" [_thread_blocked, id=14600, stack(0x0000006244800000,0x0000006244900000)]
  0x000001a57f4b97d0 JavaThread "File watcher server" daemon [_thread_blocked, id=10112, stack(0x0000006244900000,0x0000006244a00000)]
  0x000001a57f4bac10 JavaThread "File watcher consumer" daemon [_thread_blocked, id=21788, stack(0x0000006244a00000,0x0000006244b00000)]
  0x000001a5002d1b60 JavaThread "Cache worker for file content cache (H:\Users\zhang\.gradle\caches\8.5\fileContent)" [_thread_blocked, id=38380, stack(0x0000006245100000,0x0000006245200000)]
  0x000001a5002d43e0 JavaThread "Cache worker for execution history cache (H:\Users\zhang\.gradle\caches\8.5\executionHistory)" [_thread_blocked, id=11880, stack(0x0000006245200000,0x0000006245300000)]
  0x000001a5002d2fa0 JavaThread "jar transforms" [_thread_blocked, id=18496, stack(0x0000006245300000,0x0000006245400000)]
  0x000001a5002d48f0 JavaThread "jar transforms Thread 2" [_thread_blocked, id=19836, stack(0x0000006245400000,0x0000006245500000)]
  0x000001a5002d80a0 JavaThread "jar transforms Thread 3" [_thread_blocked, id=29512, stack(0x0000006245600000,0x0000006245700000)]
  0x000001a57f4b8390 JavaThread "jar transforms Thread 4" [_thread_blocked, id=20840, stack(0x000000624a500000,0x000000624a600000)]
  0x000001a57a3ef800 JavaThread "jar transforms Thread 5" [_thread_blocked, id=28412, stack(0x000000624a600000,0x000000624a700000)]
  0x000001a501681440 JavaThread "jar transforms Thread 6" [_thread_blocked, id=1700, stack(0x000000624a700000,0x000000624a800000)]
  0x000001a5016832a0 JavaThread "jar transforms Thread 7" [_thread_blocked, id=33240, stack(0x000000624a800000,0x000000624a900000)]
  0x000001a5016837b0 JavaThread "jar transforms Thread 8" [_thread_blocked, id=43960, stack(0x000000624a900000,0x000000624aa00000)]
  0x000001a501683cc0 JavaThread "jar transforms Thread 9" [_thread_blocked, id=29924, stack(0x000000624aa00000,0x000000624ab00000)]
  0x000001a501680a20 JavaThread "jar transforms Thread 10" [_thread_blocked, id=34968, stack(0x000000624ab00000,0x000000624ac00000)]
  0x000001a501681950 JavaThread "jar transforms Thread 11" [_thread_blocked, id=4948, stack(0x000000624ac00000,0x000000624ad00000)]
  0x000001a501680f30 JavaThread "jar transforms Thread 12" [_thread_blocked, id=29896, stack(0x000000624ad00000,0x000000624ae00000)]
  0x000001a502935960 JavaThread "Memory manager" [_thread_blocked, id=16864, stack(0x0000006246300000,0x0000006246400000)]
  0x000001a50292f930 JavaThread "Daemon Thread 3" [_thread_blocked, id=26276, stack(0x0000006241b00000,0x0000006241c00000)]
  0x000001a50292f420 JavaThread "Daemon worker Thread 3" [_thread_blocked, id=39116, stack(0x0000006243100000,0x0000006243200000)]
  0x000001a502bdd300 JavaThread "Handler for socket connection from /127.0.0.1:53799 to /127.0.0.1:54071" [_thread_in_native, id=7348, stack(0x0000006241c00000,0x0000006241d00000)]
  0x000001a502bdc3d0 JavaThread "Cancel handler" [_thread_blocked, id=29164, stack(0x0000006241d00000,0x0000006241e00000)]
  0x000001a502bdd810 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:53799 to /127.0.0.1:54071" [_thread_blocked, id=23056, stack(0x0000006243200000,0x0000006243300000)]
  0x000001a502bdc8e0 JavaThread "Stdin handler" [_thread_blocked, id=36684, stack(0x0000006243f00000,0x0000006244000000)]
  0x000001a502bddd20 JavaThread "Daemon client event forwarder" [_thread_blocked, id=21684, stack(0x0000006244000000,0x0000006244100000)]
  0x000001a502bda570 JavaThread "Cache worker for checksums cache (P:\tmp\AndroidDialer-master (2)\AndroidDialer-master\.gradle\8.5\checksums)" [_thread_blocked, id=6884, stack(0x0000006244200000,0x0000006244300000)]
  0x000001a502bde230 JavaThread "Cache worker for file hash cache (P:\tmp\AndroidDialer-master (2)\AndroidDialer-master\.gradle\8.5\fileHashes)" [_thread_blocked, id=15792, stack(0x0000006244300000,0x0000006244400000)]
  0x000001a502bdaa80 JavaThread "Cache worker for cache directory md-supplier (H:\Users\zhang\.gradle\caches\8.5\md-supplier)" [_thread_blocked, id=18540, stack(0x0000006244400000,0x0000006244500000)]
  0x000001a502bde740 JavaThread "Cache worker for cache directory md-rule (H:\Users\zhang\.gradle\caches\8.5\md-rule)" [_thread_blocked, id=14624, stack(0x0000006244500000,0x0000006244600000)]
  0x000001a502bdec50 JavaThread "Cache worker for dependencies-accessors (P:\tmp\AndroidDialer-master (2)\AndroidDialer-master\.gradle\8.5\dependencies-accessors)" [_thread_blocked, id=10840, stack(0x0000006244d00000,0x0000006244e00000)]
  0x000001a502bdb4a0 JavaThread "Cache worker for Build Output Cleanup Cache (P:\tmp\AndroidDialer-master (2)\AndroidDialer-master\.gradle\buildOutputCleanup)" [_thread_blocked, id=10108, stack(0x0000006244f00000,0x0000006245000000)]
  0x000001a502bdf160 JavaThread "Unconstrained build operations" [_thread_blocked, id=43732, stack(0x0000006245000000,0x0000006245100000)]
  0x000001a502bdf670 JavaThread "Unconstrained build operations Thread 2" [_thread_blocked, id=6024, stack(0x0000006245500000,0x0000006245600000)]
  0x000001a502bdfb80 JavaThread "Unconstrained build operations Thread 3" [_thread_blocked, id=42848, stack(0x0000006245700000,0x0000006245800000)]
  0x000001a502be0ab0 JavaThread "Unconstrained build operations Thread 4" [_thread_blocked, id=44256, stack(0x0000006245900000,0x0000006245a00000)]
  0x000001a502be05a0 JavaThread "Unconstrained build operations Thread 5" [_thread_blocked, id=13432, stack(0x0000006245a00000,0x0000006245b00000)]
  0x000001a502be0fc0 JavaThread "Unconstrained build operations Thread 6" [_thread_blocked, id=38472, stack(0x0000006245b00000,0x0000006245c00000)]
  0x000001a502be19e0 JavaThread "Unconstrained build operations Thread 7" [_thread_blocked, id=7632, stack(0x0000006245c00000,0x0000006245d00000)]
  0x000001a57a3ec050 JavaThread "Unconstrained build operations Thread 8" [_thread_blocked, id=6636, stack(0x0000006245d00000,0x0000006245e00000)]
  0x000001a57a3efd10 JavaThread "Unconstrained build operations Thread 9" [_thread_blocked, id=15992, stack(0x0000006245e00000,0x0000006245f00000)]
  0x000001a57a3ee3c0 JavaThread "Unconstrained build operations Thread 10" [_thread_blocked, id=10712, stack(0x0000006245f00000,0x0000006246000000)]
  0x000001a57a3ee8d0 JavaThread "Unconstrained build operations Thread 11" [_thread_blocked, id=37084, stack(0x0000006246000000,0x0000006246100000)]
  0x000001a57a3ef2f0 JavaThread "included builds" [_thread_blocked, id=17808, stack(0x0000006246200000,0x0000006246300000)]
  0x000001a57a3edeb0 JavaThread "Execution worker" [_thread_blocked, id=17636, stack(0x0000006246400000,0x0000006246500000)]
  0x000001a57a3f0220 JavaThread "Execution worker Thread 2" [_thread_blocked, id=27608, stack(0x0000006246500000,0x0000006246600000)]
  0x000001a57a3ed9a0 JavaThread "Execution worker Thread 3" [_thread_blocked, id=24064, stack(0x0000006246600000,0x0000006246700000)]
  0x000001a57a3f0730 JavaThread "Execution worker Thread 4" [_thread_blocked, id=42100, stack(0x0000006246700000,0x0000006246800000)]
  0x000001a57a3ecf80 JavaThread "Execution worker Thread 5" [_thread_blocked, id=42956, stack(0x0000006246800000,0x0000006246900000)]
  0x000001a57a3f1150 JavaThread "Execution worker Thread 6" [_thread_blocked, id=19780, stack(0x0000006246900000,0x0000006246a00000)]
  0x000001a57a3f1660 JavaThread "Execution worker Thread 7" [_thread_blocked, id=20596, stack(0x0000006246a00000,0x0000006246b00000)]
  0x000001a57a3f2590 JavaThread "Execution worker Thread 8" [_thread_blocked, id=40764, stack(0x0000006246b00000,0x0000006246c00000)]
  0x000001a57a3f2fb0 JavaThread "Execution worker Thread 9" [_thread_blocked, id=1532, stack(0x0000006246c00000,0x0000006246d00000)]
  0x000001a57a3f34c0 JavaThread "Execution worker Thread 10" [_thread_blocked, id=40908, stack(0x0000006246d00000,0x0000006246e00000)]
  0x000001a57a3f39d0 JavaThread "Execution worker Thread 11" [_thread_blocked, id=37436, stack(0x0000006246e00000,0x0000006246f00000)]
  0x000001a57a3f1b70 JavaThread "Cache worker for execution history cache (P:\tmp\AndroidDialer-master (2)\AndroidDialer-master\.gradle\8.5\executionHistory)" [_thread_blocked, id=21284, stack(0x0000006246f00000,0x0000006247000000)]
  0x000001a57a3ed490 JavaThread "Unconstrained build operations Thread 12" [_thread_blocked, id=44892, stack(0x0000006247000000,0x0000006247100000)]
  0x000001a57a3f2080 JavaThread "Unconstrained build operations Thread 13" [_thread_blocked, id=24040, stack(0x0000006247100000,0x0000006247200000)]
  0x000001a502930d70 JavaThread "Unconstrained build operations Thread 14" [_thread_blocked, id=14052, stack(0x0000006247300000,0x0000006247400000)]
  0x000001a5029326c0 JavaThread "WorkerExecutor Queue" [_thread_blocked, id=37672, stack(0x0000006247400000,0x0000006247500000)]
  0x000001a502931280 JavaThread "Unconstrained build operations Thread 15" [_thread_blocked, id=30664, stack(0x0000006247600000,0x0000006247700000)]
  0x000001a50292ef10 JavaThread "WorkerExecutor Queue Thread 3" [_thread_blocked, id=3600, stack(0x0000006247700000,0x0000006247800000)]
  0x000001a502934a30 JavaThread "WorkerExecutor Queue Thread 4" [_thread_blocked, id=25988, stack(0x0000006247800000,0x0000006247900000)]
  0x000001a502932bd0 JavaThread "Unconstrained build operations Thread 16" [_thread_blocked, id=8244, stack(0x0000006247900000,0x0000006247a00000)]
  0x000001a502934520 JavaThread "WorkerExecutor Queue Thread 5" [_thread_blocked, id=25204, stack(0x0000006247a00000,0x0000006247b00000)]
  0x000001a502934f40 JavaThread "WorkerExecutor Queue Thread 6" [_thread_blocked, id=28932, stack(0x0000006247b00000,0x0000006247c00000)]
  0x000001a5029330e0 JavaThread "WorkerExecutor Queue Thread 7" [_thread_blocked, id=9472, stack(0x0000006247c00000,0x0000006247d00000)]
  0x000001a5029335f0 JavaThread "Unconstrained build operations Thread 17" [_thread_blocked, id=42424, stack(0x0000006247d00000,0x0000006247e00000)]
  0x000001a502931790 JavaThread "Unconstrained build operations Thread 18" [_thread_blocked, id=43968, stack(0x0000006247e00000,0x0000006247f00000)]
  0x000001a502931ca0 JavaThread "WorkerExecutor Queue Thread 8" [_thread_blocked, id=17420, stack(0x0000006248100000,0x0000006248200000)]
  0x000001a502936380 JavaThread "WorkerExecutor Queue Thread 9" [_thread_blocked, id=380, stack(0x0000006248200000,0x0000006248300000)]
  0x000001a502936890 JavaThread "Unconstrained build operations Thread 19" [_thread_blocked, id=5988, stack(0x0000006248300000,0x0000006248400000)]
  0x000001a5002d4e00 JavaThread "Unconstrained build operations Thread 20" [_thread_blocked, id=10724, stack(0x0000006248400000,0x0000006248500000)]
  0x000001a5002d6c60 JavaThread "Unconstrained build operations Thread 21" [_thread_blocked, id=26876, stack(0x0000006248500000,0x0000006248600000)]
  0x000001a5002d6750 JavaThread "Unconstrained build operations Thread 22" [_thread_blocked, id=34128, stack(0x0000006248600000,0x0000006248700000)]
  0x000001a5002d34b0 JavaThread "Unconstrained build operations Thread 23" [_thread_blocked, id=12696, stack(0x0000006248700000,0x0000006248800000)]
  0x000001a5002d39c0 JavaThread "Unconstrained build operations Thread 24" [_thread_blocked, id=42376, stack(0x0000006248800000,0x0000006248900000)]
  0x000001a5002d7680 JavaThread "Unconstrained build operations Thread 25" [_thread_blocked, id=6444, stack(0x0000006248900000,0x0000006248a00000)]
  0x000001a5002d8ac0 JavaThread "Unconstrained build operations Thread 26" [_thread_blocked, id=39320, stack(0x0000006248a00000,0x0000006248b00000)]
  0x000001a57f4bb630 JavaThread "Unconstrained build operations Thread 27" [_thread_blocked, id=38020, stack(0x0000006248b00000,0x0000006248c00000)]
  0x000001a57f4b92c0 JavaThread "Unconstrained build operations Thread 28" [_thread_blocked, id=29236, stack(0x0000006248c00000,0x0000006248d00000)]
  0x000001a57f4b9ce0 JavaThread "Unconstrained build operations Thread 29" [_thread_blocked, id=20980, stack(0x0000006248d00000,0x0000006248e00000)]
  0x000001a57f4ba700 JavaThread "Unconstrained build operations Thread 30" [_thread_blocked, id=11364, stack(0x0000006248e00000,0x0000006248f00000)]
  0x000001a57f4bb120 JavaThread "Unconstrained build operations Thread 31" [_thread_blocked, id=5812, stack(0x0000006248f00000,0x0000006249000000)]
  0x000001a57f4bbb40 JavaThread "Unconstrained build operations Thread 32" [_thread_blocked, id=19592, stack(0x0000006249000000,0x0000006249100000)]
  0x000001a5016841d0 JavaThread "Unconstrained build operations Thread 33" [_thread_blocked, id=20884, stack(0x0000006249100000,0x0000006249200000)]
  0x000001a501682370 JavaThread "Unconstrained build operations Thread 34" [_thread_blocked, id=10896, stack(0x0000006249200000,0x0000006249300000)]
  0x000001a501682880 JavaThread "Unconstrained build operations Thread 35" [_thread_blocked, id=25004, stack(0x0000006249300000,0x0000006249400000)]
  0x000001a501682d90 JavaThread "Unconstrained build operations Thread 36" [_thread_blocked, id=27452, stack(0x0000006249400000,0x0000006249500000)]
  0x000001a5029321b0 JavaThread "Unconstrained build operations Thread 37" [_thread_blocked, id=30228, stack(0x0000006249500000,0x0000006249600000)]
  0x000001a502934010 JavaThread "Unconstrained build operations Thread 38" [_thread_blocked, id=26476, stack(0x0000006249600000,0x0000006249700000)]
  0x000001a502930860 JavaThread "Unconstrained build operations Thread 39" [_thread_blocked, id=22656, stack(0x0000006249700000,0x0000006249800000)]
  0x000001a57a3f2aa0 JavaThread "Unconstrained build operations Thread 40" [_thread_blocked, id=5432, stack(0x0000006249800000,0x0000006249900000)]
  0x000001a502538d50 JavaThread "Unconstrained build operations Thread 41" [_thread_blocked, id=36580, stack(0x0000006249900000,0x0000006249a00000)]
  0x000001a502536ef0 JavaThread "Unconstrained build operations Thread 42" [_thread_blocked, id=6328, stack(0x0000006249a00000,0x0000006249b00000)]
  0x000001a502537400 JavaThread "WorkerExecutor Queue Thread 10" [_thread_blocked, id=41972, stack(0x0000006249b00000,0x0000006249c00000)]
  0x000001a502538330 JavaThread "WorkerExecutor Queue Thread 12" [_thread_blocked, id=10852, stack(0x0000006249d00000,0x0000006249e00000)]
  0x000001a50253d430 JavaThread "WorkerExecutor Queue Thread 13" [_thread_blocked, id=10012, stack(0x0000006249e00000,0x0000006249f00000)]
  0x000001a50253ca10 JavaThread "pool-6-thread-1" [_thread_blocked, id=38216, stack(0x0000006249f00000,0x000000624a000000)]
  0x000001a50253d940 JavaThread "stderr" [_thread_in_native, id=20556, stack(0x000000624a000000,0x000000624a100000)]
  0x000001a50253c500 JavaThread "stdout" [_thread_in_native, id=33328, stack(0x000000624a100000,0x000000624a200000)]
  0x000001a50253cf20 JavaThread "stderr" [_thread_in_native, id=11184, stack(0x000000624a200000,0x000000624a300000)]
  0x000001a50253a190 JavaThread "stdout" [_thread_in_native, id=10512, stack(0x000000624a300000,0x000000624a400000)]
  0x000001a50253a6a0 JavaThread "stderr" [_thread_in_native, id=3392, stack(0x000000624a400000,0x000000624a500000)]
  0x000001a50253b0c0 JavaThread "stdout" [_thread_in_native, id=17664, stack(0x000000624ae00000,0x000000624af00000)]
  0x000001a50253b5d0 JavaThread "stderr" [_thread_in_native, id=31656, stack(0x000000624af00000,0x000000624b000000)]
  0x000001a5002d2580 JavaThread "stdout" [_thread_in_native, id=36360, stack(0x000000624b000000,0x000000624b100000)]
  0x000001a5038c6d00 JavaThread "stderr" [_thread_in_native, id=24232, stack(0x000000624b100000,0x000000624b200000)]
  0x000001a5038c67f0 JavaThread "stdout" [_thread_in_native, id=30628, stack(0x000000624b200000,0x000000624b300000)]
  0x000001a5038c8650 JavaThread "stderr" [_thread_in_native, id=20948, stack(0x000000624b300000,0x000000624b400000)]
  0x000001a5038c9fa0 JavaThread "stdout" [_thread_in_native, id=16272, stack(0x000000624b400000,0x000000624b500000)]
  0x000001a5038c9a90 JavaThread "stderr" [_thread_in_native, id=25292, stack(0x000000624b500000,0x000000624b600000)]
  0x000001a5038c8b60 JavaThread "stdout" [_thread_in_native, id=13348, stack(0x000000624b600000,0x000000624b700000)]
  0x000001a5038c9580 JavaThread "stderr" [_thread_in_native, id=20588, stack(0x000000624b700000,0x000000624b800000)]
  0x000001a5038c7210 JavaThread "stdout" [_thread_in_native, id=34280, stack(0x000000624b800000,0x000000624b900000)]
  0x000001a50253bae0 JavaThread "Unconstrained build operations Thread 43" [_thread_blocked, id=32592, stack(0x0000006247500000,0x0000006247600000)]
  0x000001a502539770 JavaThread "Unconstrained build operations Thread 44" [_thread_blocked, id=12772, stack(0x000000624b900000,0x000000624ba00000)]
  0x000001a50292fe40 JavaThread "Unconstrained build operations Thread 45" [_thread_blocked, id=39444, stack(0x000000624ba00000,0x000000624bb00000)]
  0x000001a5068c3ca0 JavaThread "Unconstrained build operations Thread 46" [_thread_blocked, id=16168, stack(0x000000624bb00000,0x000000624bc00000)]
  0x000001a5068c4bd0 JavaThread "Unconstrained build operations Thread 47" [_thread_blocked, id=25672, stack(0x000000624bc00000,0x000000624bd00000)]
  0x000001a5068c2860 JavaThread "Unconstrained build operations Thread 48" [_thread_blocked, id=16408, stack(0x000000624bd00000,0x000000624be00000)]
  0x000001a5068c3280 JavaThread "Unconstrained build operations Thread 49" [_thread_blocked, id=32128, stack(0x000000624be00000,0x000000624bf00000)]
  0x000001a5068c50e0 JavaThread "Unconstrained build operations Thread 50" [_thread_blocked, id=31008, stack(0x000000624bf00000,0x000000624c000000)]
  0x000001a5068c2d70 JavaThread "Unconstrained build operations Thread 51" [_thread_blocked, id=34584, stack(0x000000624c000000,0x000000624c100000)]
  0x000001a5068c41b0 JavaThread "Unconstrained build operations Thread 52" [_thread_blocked, id=21060, stack(0x000000624c100000,0x000000624c200000)]
  0x000001a5068c1930 JavaThread "Unconstrained build operations Thread 53" [_thread_blocked, id=34944, stack(0x000000624c200000,0x000000624c300000)]
  0x000001a5068c1e40 JavaThread "Unconstrained build operations Thread 54" [_thread_blocked, id=3884, stack(0x000000624c300000,0x000000624c400000)]
  0x000001a5068c6a30 JavaThread "Unconstrained build operations Thread 55" [_thread_blocked, id=23940, stack(0x0000006244100000,0x0000006244200000)]
  0x000001a5068c6010 JavaThread "Unconstrained build operations Thread 56" [_thread_blocked, id=9600, stack(0x0000006244e00000,0x0000006244f00000)]
  0x000001a5068c3790 JavaThread "Unconstrained build operations Thread 57" [_thread_blocked, id=15180, stack(0x000000624c400000,0x000000624c500000)]
  0x000001a5068c6520 JavaThread "Unconstrained build operations Thread 58" [_thread_blocked, id=16656, stack(0x000000624c600000,0x000000624c700000)]
  0x000001a5068c55f0 JavaThread "Unconstrained build operations Thread 59" [_thread_blocked, id=43628, stack(0x000000624c500000,0x000000624c600000)]
  0x000001a5068c6f40 JavaThread "Unconstrained build operations Thread 60" [_thread_blocked, id=43292, stack(0x000000624c700000,0x000000624c800000)]
  0x000001a5068c7450 JavaThread "Unconstrained build operations Thread 61" [_thread_blocked, id=12240, stack(0x000000624c800000,0x000000624c900000)]
  0x000001a5068c7960 JavaThread "Unconstrained build operations Thread 62" [_thread_blocked, id=38480, stack(0x000000624c900000,0x000000624ca00000)]
  0x000001a5068c7e70 JavaThread "Unconstrained build operations Thread 63" [_thread_blocked, id=12564, stack(0x000000624cb00000,0x000000624cc00000)]
  0x000001a5068c46c0 JavaThread "Unconstrained build operations Thread 64" [_thread_blocked, id=28432, stack(0x000000624cc00000,0x000000624cd00000)]
  0x000001a5068c8380 JavaThread "Unconstrained build operations Thread 65" [_thread_blocked, id=15824, stack(0x000000624cd00000,0x000000624ce00000)]
  0x000001a5068c8890 JavaThread "Unconstrained build operations Thread 66" [_thread_blocked, id=23180, stack(0x000000624ce00000,0x000000624cf00000)]
  0x000001a5068c8da0 JavaThread "Unconstrained build operations Thread 67" [_thread_blocked, id=33532, stack(0x000000624cf00000,0x000000624d000000)]
  0x000001a5038c9070 JavaThread "Unconstrained build operations Thread 68" [_thread_blocked, id=32116, stack(0x000000624d000000,0x000000624d100000)]
  0x000001a5068c92b0 JavaThread "Unconstrained build operations Thread 69" [_thread_blocked, id=37724, stack(0x000000624ca00000,0x000000624cb00000)]
  0x000001a503ea6330 JavaThread "Unconstrained build operations Thread 70" [_thread_blocked, id=24516, stack(0x000000624d100000,0x000000624d200000)]
  0x000001a503ea3fc0 JavaThread "Unconstrained build operations Thread 71" [_thread_blocked, id=17100, stack(0x000000624d200000,0x000000624d300000)]
  0x000001a503ea44d0 JavaThread "Unconstrained build operations Thread 72" [_thread_blocked, id=4508, stack(0x000000624d300000,0x000000624d400000)]
  0x000001a503ea6840 JavaThread "Unconstrained build operations Thread 73" [_thread_blocked, id=37160, stack(0x000000624d400000,0x000000624d500000)]
  0x000001a503ea5400 JavaThread "Unconstrained build operations Thread 74" [_thread_blocked, id=19408, stack(0x000000624d500000,0x000000624d600000)]
  0x000001a503ea5e20 JavaThread "Unconstrained build operations Thread 75" [_thread_blocked, id=39052, stack(0x000000624d600000,0x000000624d700000)]
  0x000001a503ea5910 JavaThread "Unconstrained build operations Thread 76" [_thread_blocked, id=16136, stack(0x000000624d700000,0x000000624d800000)]
  0x000001a503ea3ab0 JavaThread "Unconstrained build operations Thread 77" [_thread_blocked, id=42840, stack(0x000000624d800000,0x000000624d900000)]
  0x000001a503ea3090 JavaThread "Unconstrained build operations Thread 78" [_thread_blocked, id=256, stack(0x000000624d900000,0x000000624da00000)]
  0x000001a503ea7260 JavaThread "Unconstrained build operations Thread 79" [_thread_blocked, id=4876, stack(0x000000624dc00000,0x000000624dd00000)]
  0x000001a503ea7c80 JavaThread "Unconstrained build operations Thread 80" [_thread_blocked, id=8160, stack(0x000000624dd00000,0x000000624de00000)]
  0x000001a503ea7770 JavaThread "Unconstrained build operations Thread 81" [_thread_blocked, id=25112, stack(0x000000624de00000,0x000000624df00000)]
  0x000001a503ea6d50 JavaThread "Unconstrained build operations Thread 83" [_thread_blocked, id=44592, stack(0x000000624df00000,0x000000624e000000)]
  0x000001a503ea8190 JavaThread "Unconstrained build operations Thread 82" [_thread_blocked, id=2284, stack(0x000000624e000000,0x000000624e100000)]
  0x000001a503ea49e0 JavaThread "Unconstrained build operations Thread 84" [_thread_blocked, id=28584, stack(0x000000624e100000,0x000000624e200000)]
  0x000001a503ea86a0 JavaThread "Unconstrained build operations Thread 85" [_thread_blocked, id=22868, stack(0x000000624e200000,0x000000624e300000)]
  0x000001a503ea4ef0 JavaThread "Unconstrained build operations Thread 86" [_thread_blocked, id=27440, stack(0x000000624e300000,0x000000624e400000)]
  0x000001a503ea90c0 JavaThread "Unconstrained build operations Thread 87" [_thread_blocked, id=14144, stack(0x000000624e400000,0x000000624e500000)]
  0x000001a503ea8bb0 JavaThread "Unconstrained build operations Thread 88" [_thread_blocked, id=35980, stack(0x000000624e500000,0x000000624e600000)]
  0x000001a503ea95d0 JavaThread "Unconstrained build operations Thread 89" [_thread_blocked, id=32664, stack(0x000000624e600000,0x000000624e700000)]
  0x000001a503ea9ae0 JavaThread "Unconstrained build operations Thread 90" [_thread_blocked, id=22316, stack(0x000000624e700000,0x000000624e800000)]
  0x000001a503ea9ff0 JavaThread "Unconstrained build operations Thread 91" [_thread_blocked, id=8780, stack(0x000000624e800000,0x000000624e900000)]
  0x000001a503eaa500 JavaThread "Unconstrained build operations Thread 92" [_thread_blocked, id=35752, stack(0x000000624e900000,0x000000624ea00000)]
  0x000001a503eaaa10 JavaThread "Unconstrained build operations Thread 93" [_thread_blocked, id=15896, stack(0x000000624ea00000,0x000000624eb00000)]
  0x000001a5038c7c30 JavaThread "Unconstrained build operations Thread 94" [_thread_blocked, id=5156, stack(0x000000624eb00000,0x000000624ec00000)]
  0x000001a5027f9590 JavaThread "Unconstrained build operations Thread 95" [_thread_blocked, id=13644, stack(0x000000624ec00000,0x000000624ed00000)]
  0x000001a5027fa9d0 JavaThread "Unconstrained build operations Thread 96" [_thread_blocked, id=24648, stack(0x000000624ed00000,0x000000624ee00000)]
  0x000001a5027f7220 JavaThread "Unconstrained build operations Thread 97" [_thread_blocked, id=35504, stack(0x000000624ee00000,0x000000624ef00000)]
  0x000001a5027f7c40 JavaThread "Unconstrained build operations Thread 99" [_thread_blocked, id=34728, stack(0x000000624ef00000,0x000000624f000000)]
  0x000001a5027f9aa0 JavaThread "Unconstrained build operations Thread 98" [_thread_blocked, id=43812, stack(0x000000624f000000,0x000000624f100000)]
  0x000001a5027faee0 JavaThread "Unconstrained build operations Thread 100" [_thread_blocked, id=34756, stack(0x000000624f100000,0x000000624f200000)]
  0x000001a5027f8150 JavaThread "WorkerExecutor Queue Thread 14" [_thread_blocked, id=20544, stack(0x000000624f200000,0x000000624f300000)]
  0x000001a5027f8b70 JavaThread "Unconstrained build operations Thread 101" [_thread_blocked, id=22448, stack(0x000000624f300000,0x000000624f400000)]
  0x000001a5027f9080 JavaThread "Unconstrained build operations Thread 102" [_thread_blocked, id=39276, stack(0x000000624f400000,0x000000624f500000)]
  0x000001a5027fd250 JavaThread "Unconstrained build operations Thread 103" [_thread_blocked, id=30220, stack(0x000000624f500000,0x000000624f600000)]
  0x000001a5027fd760 JavaThread "Unconstrained build operations Thread 104" [_thread_blocked, id=27028, stack(0x000000624f600000,0x000000624f700000)]
  0x000001a5027fc320 JavaThread "Unconstrained build operations Thread 105" [_thread_blocked, id=19152, stack(0x000000624f700000,0x000000624f800000)]
  0x000001a5027fdc70 JavaThread "Unconstrained build operations Thread 106" [_thread_blocked, id=5248, stack(0x000000624f800000,0x000000624f900000)]
  0x000001a5027fe180 JavaThread "Unconstrained build operations Thread 107" [_thread_blocked, id=18612, stack(0x000000624f900000,0x000000624fa00000)]
  0x000001a5027ff0b0 JavaThread "Unconstrained build operations Thread 108" [_thread_blocked, id=30180, stack(0x000000624fa00000,0x000000624fb00000)]
  0x000001a5027fe690 JavaThread "Unconstrained build operations Thread 109" [_thread_blocked, id=22576, stack(0x000000624fb00000,0x000000624fc00000)]
  0x000001a5027fbe10 JavaThread "Unconstrained build operations Thread 110" [_thread_blocked, id=9204, stack(0x000000624fc00000,0x000000624fd00000)]
  0x000001a5027feba0 JavaThread "Unconstrained build operations Thread 111" [_thread_blocked, id=29088, stack(0x000000624fd00000,0x000000624fe00000)]
  0x000001a5027fc830 JavaThread "Unconstrained build operations Thread 112" [_thread_blocked, id=39972, stack(0x000000624fe00000,0x000000624ff00000)]
  0x000001a5027ff5c0 JavaThread "Unconstrained build operations Thread 113" [_thread_blocked, id=2728, stack(0x000000624ff00000,0x0000006250000000)]
  0x000001a5027ffad0 JavaThread "Unconstrained build operations Thread 114" [_thread_blocked, id=32780, stack(0x0000006250000000,0x0000006250100000)]
  0x000001a5027fffe0 JavaThread "Unconstrained build operations Thread 115" [_thread_blocked, id=2568, stack(0x0000006250100000,0x0000006250200000)]
  0x000001a5028004f0 JavaThread "Unconstrained build operations Thread 116" [_thread_blocked, id=6892, stack(0x0000006250200000,0x0000006250300000)]
  0x000001a5027fcd40 JavaThread "Unconstrained build operations Thread 117" [_thread_blocked, id=18200, stack(0x0000006250300000,0x0000006250400000)]
  0x000001a502803280 JavaThread "Unconstrained build operations Thread 118" [_thread_blocked, id=9616, stack(0x0000006250400000,0x0000006250500000)]
  0x000001a502803790 JavaThread "Unconstrained build operations Thread 119" [_thread_blocked, id=44844, stack(0x0000006250500000,0x0000006250600000)]
  0x000001a502803ca0 JavaThread "Unconstrained build operations Thread 120" [_thread_blocked, id=17600, stack(0x0000006250600000,0x0000006250700000)]
  0x000001a5027f9fb0 JavaThread "Cache worker for Java compile cache (H:\Users\zhang\.gradle\caches\8.5\javaCompile)" [_thread_blocked, id=19548, stack(0x0000006249c00000,0x0000006249d00000)]
  0x000001a502800f10 JavaThread "Build operations" [_thread_blocked, id=29572, stack(0x0000006250700000,0x0000006250800000)]
  0x000001a502802350 JavaThread "Build operations Thread 2" [_thread_blocked, id=15904, stack(0x0000006250800000,0x0000006250900000)]
  0x000001a502801420 JavaThread "Build operations Thread 3" [_thread_blocked, id=25220, stack(0x0000006250900000,0x0000006250a00000)]
  0x000001a502801e40 JavaThread "Build operations Thread 4" [_thread_blocked, id=22096, stack(0x0000006250a00000,0x0000006250b00000)]
  0x000001a5028055f0 JavaThread "Build operations Thread 5" [_thread_blocked, id=20612, stack(0x0000006250b00000,0x0000006250c00000)]
  0x000001a502805b00 JavaThread "Build operations Thread 6" [_thread_blocked, id=23920, stack(0x0000006250c00000,0x0000006250d00000)]
  0x000001a502806010 JavaThread "Build operations Thread 7" [_thread_blocked, id=24900, stack(0x0000006250d00000,0x0000006250e00000)]
  0x000001a502806520 JavaThread "Build operations Thread 8" [_thread_blocked, id=16996, stack(0x0000006250e00000,0x0000006250f00000)]
  0x000001a502802d70 JavaThread "Build operations Thread 9" [_thread_blocked, id=39432, stack(0x0000006250f00000,0x0000006251000000)]
  0x000001a502806a30 JavaThread "Build operations Thread 10" [_thread_blocked, id=26936, stack(0x0000006251000000,0x0000006251100000)]
  0x000001a5002d5d30 JavaThread "Build operations Thread 11" [_thread_blocked, id=40784, stack(0x0000006251100000,0x0000006251200000)]
  0x000001a50623dbd0 JavaThread "Build operations Thread 12" [_thread_blocked, id=18336, stack(0x0000006251200000,0x0000006251300000)]
  0x000001a50447c420 JavaThread "C2 CompilerThread1" daemon [_thread_blocked, id=10200, stack(0x0000006246100000,0x0000006246200000)]
  0x000001a506f0aed0 JavaThread "C2 CompilerThread2" daemon [_thread_blocked, id=10808, stack(0x0000006247200000,0x0000006247300000)]
  0x000001a50e85f070 JavaThread "WorkerExecutor Queue Thread 15" [_thread_blocked, id=15048, stack(0x0000006251600000,0x0000006251700000)]

Other Threads:
=>0x000001a53844dee0 VMThread "VM Thread" [stack: 0x0000006242400000,0x0000006242500000] [id=14960]
  0x000001a514eccc10 WatcherThread [stack: 0x0000006243000000,0x0000006243100000] [id=13344]
  0x000001a514e9ffa0 GCTaskThread "GC Thread#0" [stack: 0x0000006241f00000,0x0000006242000000] [id=25800]
  0x000001a57f1ae7a0 GCTaskThread "GC Thread#1" [stack: 0x0000006243300000,0x0000006243400000] [id=5176]
  0x000001a57f1aea60 GCTaskThread "GC Thread#2" [stack: 0x0000006243400000,0x0000006243500000] [id=5428]
  0x000001a57f1aed20 GCTaskThread "GC Thread#3" [stack: 0x0000006243500000,0x0000006243600000] [id=1628]
  0x000001a57f1aefe0 GCTaskThread "GC Thread#4" [stack: 0x0000006243600000,0x0000006243700000] [id=21920]
  0x000001a57f1af2a0 GCTaskThread "GC Thread#5" [stack: 0x0000006243700000,0x0000006243800000] [id=38036]
  0x000001a57f37d440 GCTaskThread "GC Thread#6" [stack: 0x0000006243800000,0x0000006243900000] [id=27844]
  0x000001a57a8221b0 GCTaskThread "GC Thread#7" [stack: 0x0000006243900000,0x0000006243a00000] [id=40160]
  0x000001a57a822470 GCTaskThread "GC Thread#8" [stack: 0x0000006243a00000,0x0000006243b00000] [id=29252]
  0x000001a57a822730 GCTaskThread "GC Thread#9" [stack: 0x0000006243b00000,0x0000006243c00000] [id=8048]
  0x000001a514eb1810 ConcurrentGCThread "G1 Main Marker" [stack: 0x0000006242000000,0x0000006242100000] [id=6248]
  0x000001a514eb1fd0 ConcurrentGCThread "G1 Conc#0" [stack: 0x0000006242100000,0x0000006242200000] [id=40176]
  0x000001a502eaaef0 ConcurrentGCThread "G1 Conc#1" [stack: 0x0000006244b00000,0x0000006244c00000] [id=37156]
  0x000001a5022785e0 ConcurrentGCThread "G1 Conc#2" [stack: 0x0000006244c00000,0x0000006244d00000] [id=41700]
  0x000001a5383e6920 ConcurrentGCThread "G1 Refine#0" [stack: 0x0000006242200000,0x0000006242300000] [id=23452]
  0x000001a5054f84a0 ConcurrentGCThread "G1 Refine#1" [stack: 0x0000006247f00000,0x0000006248000000] [id=13792]
  0x000001a5006b7e00 ConcurrentGCThread "G1 Refine#2" [stack: 0x0000006248000000,0x0000006248100000] [id=41224]
  0x000001a505519920 ConcurrentGCThread "G1 Refine#3" [stack: 0x000000624da00000,0x000000624db00000] [id=34732]
  0x000001a502abe1b0 ConcurrentGCThread "G1 Refine#4" [stack: 0x000000624db00000,0x000000624dc00000] [id=20592]
  0x000001a5006b80f0 ConcurrentGCThread "G1 Refine#5" [stack: 0x0000006251300000,0x0000006251400000] [id=33300]
  0x000001a500dbf540 ConcurrentGCThread "G1 Refine#6" [stack: 0x0000006251400000,0x0000006251500000] [id=35988]
  0x000001a5041aa2e0 ConcurrentGCThread "G1 Refine#7" [stack: 0x0000006251500000,0x0000006251600000] [id=34980]
  0x000001a5383e7100 ConcurrentGCThread "G1 Service" [stack: 0x0000006242300000,0x0000006242400000] [id=44352]

Threads with active compile tasks:
C2 CompilerThread0   252176 42640       4       com.android.tools.r8.ir.optimize.F::a (610 bytes)
C1 CompilerThread0   252176 43748       3       com.android.tools.r8.internal.hl::a (2961 bytes)
C2 CompilerThread1   252176 43581       4       com.android.tools.r8.internal.e00::b (193 bytes)
C2 CompilerThread2   252176 43722       4       com.android.tools.r8.internal.kf::a (1419 bytes)

VM state: at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x000001a514e2e6d0] Threads_lock - owner thread: 0x000001a53844dee0
[0x000001a514e2f570] Heap_lock - owner thread: 0x000001a50292ef10

Heap address: 0x0000000700000000, size: 4096 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x000001a539000000-0x000001a539bd0000-0x000001a539bd0000), size 12386304, SharedBaseAddress: 0x000001a539000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000001a53a000000-0x000001a57a000000, reserved size: 1073741824
Narrow klass base: 0x000001a539000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 12 total, 12 available
 Memory: 32051M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 2M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 502M
 Heap Max Capacity: 4G
 Pre-touch: Disabled
 Parallel Workers: 10
 Concurrent Workers: 3
 Concurrent Refinement Workers: 10
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 792576K, used 457353K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 13 young (26624K), 13 survivors (26624K)
 Metaspace       used 150521K, committed 151616K, reserved 1245184K
  class space    used 19148K, committed 19712K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000700000000, 0x0000000700200000, 0x0000000700200000|100%|HS|  |TAMS 0x0000000700200000, 0x0000000700000000| Complete 
|   1|0x0000000700200000, 0x0000000700400000, 0x0000000700400000|100%|HC|  |TAMS 0x0000000700400000, 0x0000000700200000| Complete 
|   2|0x0000000700400000, 0x0000000700600000, 0x0000000700600000|100%| O|  |TAMS 0x0000000700600000, 0x0000000700400000| Untracked 
|   3|0x0000000700600000, 0x0000000700800000, 0x0000000700800000|100%| O|  |TAMS 0x0000000700800000, 0x0000000700600000| Untracked 
|   4|0x0000000700800000, 0x0000000700a00000, 0x0000000700a00000|100%| O|  |TAMS 0x0000000700a00000, 0x0000000700800000| Untracked 
|   5|0x0000000700a00000, 0x0000000700ab2e00, 0x0000000700c00000| 34%| O|  |TAMS 0x0000000700ab2e00, 0x0000000700a00000| Untracked 
|   6|0x0000000700c00000, 0x0000000700e00000, 0x0000000700e00000|100%| O|  |TAMS 0x0000000700e00000, 0x0000000700c00000| Untracked 
|   7|0x0000000700e00000, 0x0000000701000000, 0x0000000701000000|100%| O|  |TAMS 0x0000000701000000, 0x0000000700e00000| Untracked 
|   8|0x0000000701000000, 0x0000000701200000, 0x0000000701200000|100%| O|  |TAMS 0x0000000701200000, 0x0000000701000000| Untracked 
|   9|0x0000000701200000, 0x0000000701400000, 0x0000000701400000|100%| O|  |TAMS 0x0000000701400000, 0x0000000701200000| Untracked 
|  10|0x0000000701400000, 0x0000000701600000, 0x0000000701600000|100%| O|  |TAMS 0x0000000701600000, 0x0000000701400000| Untracked 
|  11|0x0000000701600000, 0x0000000701800000, 0x0000000701800000|100%| O|  |TAMS 0x0000000701800000, 0x0000000701600000| Untracked 
|  12|0x0000000701800000, 0x0000000701a00000, 0x0000000701a00000|100%| O|  |TAMS 0x0000000701a00000, 0x0000000701800000| Untracked 
|  13|0x0000000701a00000, 0x0000000701c00000, 0x0000000701c00000|100%| O|  |TAMS 0x0000000701c00000, 0x0000000701a00000| Untracked 
|  14|0x0000000701c00000, 0x0000000701e00000, 0x0000000701e00000|100%| O|  |TAMS 0x0000000701e00000, 0x0000000701c00000| Untracked 
|  15|0x0000000701e00000, 0x0000000702000000, 0x0000000702000000|100%| O|  |TAMS 0x0000000702000000, 0x0000000701e00000| Untracked 
|  16|0x0000000702000000, 0x0000000702200000, 0x0000000702200000|100%| O|  |TAMS 0x0000000702200000, 0x0000000702000000| Untracked 
|  17|0x0000000702200000, 0x0000000702400000, 0x0000000702400000|100%| O|  |TAMS 0x0000000702400000, 0x0000000702200000| Untracked 
|  18|0x0000000702400000, 0x0000000702600000, 0x0000000702600000|100%| O|  |TAMS 0x0000000702600000, 0x0000000702400000| Untracked 
|  19|0x0000000702600000, 0x0000000702800000, 0x0000000702800000|100%| O|  |TAMS 0x0000000702800000, 0x0000000702600000| Untracked 
|  20|0x0000000702800000, 0x0000000702a00000, 0x0000000702a00000|100%| O|  |TAMS 0x0000000702a00000, 0x0000000702800000| Untracked 
|  21|0x0000000702a00000, 0x0000000702c00000, 0x0000000702c00000|100%| O|  |TAMS 0x0000000702c00000, 0x0000000702a00000| Untracked 
|  22|0x0000000702c00000, 0x0000000702e00000, 0x0000000702e00000|100%| O|  |TAMS 0x0000000702c00000, 0x0000000702c00000| Untracked 
|  23|0x0000000702e00000, 0x0000000703000000, 0x0000000703000000|100%| O|  |TAMS 0x0000000703000000, 0x0000000702e00000| Untracked 
|  24|0x0000000703000000, 0x0000000703200000, 0x0000000703200000|100%| O|  |TAMS 0x0000000703200000, 0x0000000703000000| Untracked 
|  25|0x0000000703200000, 0x0000000703400000, 0x0000000703400000|100%| O|  |TAMS 0x0000000703200000, 0x0000000703200000| Untracked 
|  26|0x0000000703400000, 0x0000000703600000, 0x0000000703600000|100%| O|  |TAMS 0x0000000703600000, 0x0000000703400000| Untracked 
|  27|0x0000000703600000, 0x0000000703800000, 0x0000000703800000|100%| O|  |TAMS 0x0000000703800000, 0x0000000703600000| Untracked 
|  28|0x0000000703800000, 0x0000000703a00000, 0x0000000703a00000|100%| O|  |TAMS 0x0000000703a00000, 0x0000000703800000| Untracked 
|  29|0x0000000703a00000, 0x0000000703c00000, 0x0000000703c00000|100%| O|  |TAMS 0x0000000703c00000, 0x0000000703a00000| Untracked 
|  30|0x0000000703c00000, 0x0000000703e00000, 0x0000000703e00000|100%| O|  |TAMS 0x0000000703e00000, 0x0000000703c00000| Untracked 
|  31|0x0000000703e00000, 0x0000000704000000, 0x0000000704000000|100%| O|  |TAMS 0x0000000704000000, 0x0000000703e00000| Untracked 
|  32|0x0000000704000000, 0x0000000704200000, 0x0000000704200000|100%| O|  |TAMS 0x0000000704200000, 0x0000000704000000| Untracked 
|  33|0x0000000704200000, 0x0000000704400000, 0x0000000704400000|100%| O|  |TAMS 0x0000000704400000, 0x0000000704200000| Untracked 
|  34|0x0000000704400000, 0x0000000704600000, 0x0000000704600000|100%| O|  |TAMS 0x0000000704600000, 0x0000000704400000| Untracked 
|  35|0x0000000704600000, 0x0000000704800000, 0x0000000704800000|100%| O|  |TAMS 0x0000000704800000, 0x0000000704600000| Untracked 
|  36|0x0000000704800000, 0x0000000704a00000, 0x0000000704a00000|100%| O|  |TAMS 0x0000000704a00000, 0x0000000704800000| Untracked 
|  37|0x0000000704a00000, 0x0000000704afcc00, 0x0000000704c00000| 49%| O|  |TAMS 0x0000000704afcc00, 0x0000000704a00000| Untracked 
|  38|0x0000000704c00000, 0x0000000704e00000, 0x0000000704e00000|100%| O|  |TAMS 0x0000000704e00000, 0x0000000704c00000| Untracked 
|  39|0x0000000704e00000, 0x0000000705000000, 0x0000000705000000|100%| O|  |TAMS 0x0000000705000000, 0x0000000704e00000| Untracked 
|  40|0x0000000705000000, 0x0000000705200000, 0x0000000705200000|100%| O|  |TAMS 0x0000000705200000, 0x0000000705000000| Untracked 
|  41|0x0000000705200000, 0x0000000705400000, 0x0000000705400000|100%| O|  |TAMS 0x0000000705400000, 0x0000000705200000| Untracked 
|  42|0x0000000705400000, 0x0000000705600000, 0x0000000705600000|100%| O|  |TAMS 0x0000000705600000, 0x0000000705400000| Untracked 
|  43|0x0000000705600000, 0x0000000705800000, 0x0000000705800000|100%| O|  |TAMS 0x0000000705800000, 0x0000000705600000| Untracked 
|  44|0x0000000705800000, 0x0000000705a00000, 0x0000000705a00000|100%| O|  |TAMS 0x0000000705a00000, 0x0000000705800000| Untracked 
|  45|0x0000000705a00000, 0x0000000705c00000, 0x0000000705c00000|100%| O|  |TAMS 0x0000000705c00000, 0x0000000705a00000| Untracked 
|  46|0x0000000705c00000, 0x0000000705e00000, 0x0000000705e00000|100%| O|  |TAMS 0x0000000705e00000, 0x0000000705c00000| Untracked 
|  47|0x0000000705e00000, 0x0000000706000000, 0x0000000706000000|100%| O|  |TAMS 0x0000000706000000, 0x0000000705e00000| Untracked 
|  48|0x0000000706000000, 0x0000000706200000, 0x0000000706200000|100%| O|  |TAMS 0x0000000706200000, 0x0000000706000000| Untracked 
|  49|0x0000000706200000, 0x0000000706400000, 0x0000000706400000|100%| O|  |TAMS 0x0000000706400000, 0x0000000706200000| Untracked 
|  50|0x0000000706400000, 0x0000000706600000, 0x0000000706600000|100%| O|  |TAMS 0x0000000706600000, 0x0000000706400000| Untracked 
|  51|0x0000000706600000, 0x0000000706800000, 0x0000000706800000|100%| O|  |TAMS 0x0000000706800000, 0x0000000706600000| Untracked 
|  52|0x0000000706800000, 0x0000000706a00000, 0x0000000706a00000|100%| O|  |TAMS 0x0000000706a00000, 0x0000000706800000| Untracked 
|  53|0x0000000706a00000, 0x0000000706c00000, 0x0000000706c00000|100%| O|  |TAMS 0x0000000706c00000, 0x0000000706a00000| Untracked 
|  54|0x0000000706c00000, 0x0000000706e00000, 0x0000000706e00000|100%| O|  |TAMS 0x0000000706e00000, 0x0000000706c00000| Untracked 
|  55|0x0000000706e00000, 0x0000000707000000, 0x0000000707000000|100%|HS|  |TAMS 0x0000000706e00000, 0x0000000706e00000| Complete 
|  56|0x0000000707000000, 0x0000000707200000, 0x0000000707200000|100%| O|  |TAMS 0x0000000707200000, 0x0000000707000000| Untracked 
|  57|0x0000000707200000, 0x0000000707400000, 0x0000000707400000|100%| O|  |TAMS 0x0000000707400000, 0x0000000707200000| Untracked 
|  58|0x0000000707400000, 0x0000000707600000, 0x0000000707600000|100%| O|  |TAMS 0x0000000707600000, 0x0000000707400000| Untracked 
|  59|0x0000000707600000, 0x0000000707800000, 0x0000000707800000|100%| O|  |TAMS 0x0000000707800000, 0x0000000707600000| Untracked 
|  60|0x0000000707800000, 0x0000000707a00000, 0x0000000707a00000|100%| O|  |TAMS 0x0000000707a00000, 0x0000000707800000| Untracked 
|  61|0x0000000707a00000, 0x0000000707c00000, 0x0000000707c00000|100%| O|  |TAMS 0x0000000707c00000, 0x0000000707a00000| Untracked 
|  62|0x0000000707c00000, 0x0000000707e00000, 0x0000000707e00000|100%| O|  |TAMS 0x0000000707e00000, 0x0000000707c00000| Untracked 
|  63|0x0000000707e00000, 0x0000000708000000, 0x0000000708000000|100%| O|  |TAMS 0x0000000708000000, 0x0000000707e00000| Untracked 
|  64|0x0000000708000000, 0x0000000708200000, 0x0000000708200000|100%| O|  |TAMS 0x0000000708200000, 0x0000000708000000| Untracked 
|  65|0x0000000708200000, 0x0000000708400000, 0x0000000708400000|100%| O|  |TAMS 0x0000000708400000, 0x0000000708200000| Untracked 
|  66|0x0000000708400000, 0x0000000708600000, 0x0000000708600000|100%|HS|  |TAMS 0x0000000708600000, 0x0000000708400000| Complete 
|  67|0x0000000708600000, 0x0000000708800000, 0x0000000708800000|100%| O|  |TAMS 0x0000000708800000, 0x0000000708600000| Untracked 
|  68|0x0000000708800000, 0x0000000708a00000, 0x0000000708a00000|100%| O|  |TAMS 0x0000000708a00000, 0x0000000708800000| Untracked 
|  69|0x0000000708a00000, 0x0000000708c00000, 0x0000000708c00000|100%| O|  |TAMS 0x0000000708c00000, 0x0000000708a00000| Untracked 
|  70|0x0000000708c00000, 0x0000000708e00000, 0x0000000708e00000|100%| O|  |TAMS 0x0000000708e00000, 0x0000000708c00000| Untracked 
|  71|0x0000000708e00000, 0x0000000709000000, 0x0000000709000000|100%| O|  |TAMS 0x0000000709000000, 0x0000000708e00000| Untracked 
|  72|0x0000000709000000, 0x0000000709200000, 0x0000000709200000|100%| O|  |TAMS 0x0000000709000000, 0x0000000709000000| Untracked 
|  73|0x0000000709200000, 0x0000000709400000, 0x0000000709400000|100%| O|  |TAMS 0x0000000709200000, 0x0000000709200000| Untracked 
|  74|0x0000000709400000, 0x0000000709600000, 0x0000000709600000|100%| O|  |TAMS 0x0000000709400000, 0x0000000709400000| Untracked 
|  75|0x0000000709600000, 0x0000000709800000, 0x0000000709800000|100%| O|  |TAMS 0x0000000709600000, 0x0000000709600000| Untracked 
|  76|0x0000000709800000, 0x0000000709a00000, 0x0000000709a00000|100%| O|  |TAMS 0x0000000709a00000, 0x0000000709800000| Untracked 
|  77|0x0000000709a00000, 0x0000000709c00000, 0x0000000709c00000|100%| O|  |TAMS 0x0000000709c00000, 0x0000000709a00000| Untracked 
|  78|0x0000000709c00000, 0x0000000709e00000, 0x0000000709e00000|100%| O|  |TAMS 0x0000000709d39400, 0x0000000709c00000| Untracked 
|  79|0x0000000709e00000, 0x000000070a000000, 0x000000070a000000|100%| O|  |TAMS 0x000000070a000000, 0x0000000709e00000| Untracked 
|  80|0x000000070a000000, 0x000000070a200000, 0x000000070a200000|100%| O|  |TAMS 0x000000070a200000, 0x000000070a000000| Untracked 
|  81|0x000000070a200000, 0x000000070a400000, 0x000000070a400000|100%| O|  |TAMS 0x000000070a200000, 0x000000070a200000| Untracked 
|  82|0x000000070a400000, 0x000000070a600000, 0x000000070a600000|100%| O|  |TAMS 0x000000070a600000, 0x000000070a400000| Untracked 
|  83|0x000000070a600000, 0x000000070a800000, 0x000000070a800000|100%| O|  |TAMS 0x000000070a800000, 0x000000070a600000| Untracked 
|  84|0x000000070a800000, 0x000000070aa00000, 0x000000070aa00000|100%| O|  |TAMS 0x000000070a800000, 0x000000070a800000| Untracked 
|  85|0x000000070aa00000, 0x000000070ac00000, 0x000000070ac00000|100%|HS|  |TAMS 0x000000070ac00000, 0x000000070aa00000| Complete 
|  86|0x000000070ac00000, 0x000000070ae00000, 0x000000070ae00000|100%| O|  |TAMS 0x000000070ae00000, 0x000000070ac00000| Untracked 
|  87|0x000000070ae00000, 0x000000070b000000, 0x000000070b000000|100%| O|  |TAMS 0x000000070ae00000, 0x000000070ae00000| Untracked 
|  88|0x000000070b000000, 0x000000070b200000, 0x000000070b200000|100%| O|  |TAMS 0x000000070b000000, 0x000000070b000000| Untracked 
|  89|0x000000070b200000, 0x000000070b400000, 0x000000070b400000|100%| O|  |TAMS 0x000000070b200000, 0x000000070b200000| Untracked 
|  90|0x000000070b400000, 0x000000070b600000, 0x000000070b600000|100%| O|  |TAMS 0x000000070b600000, 0x000000070b400000| Untracked 
|  91|0x000000070b600000, 0x000000070b800000, 0x000000070b800000|100%| O|  |TAMS 0x000000070b600000, 0x000000070b600000| Untracked 
|  92|0x000000070b800000, 0x000000070ba00000, 0x000000070ba00000|100%| O|  |TAMS 0x000000070b800000, 0x000000070b800000| Untracked 
|  93|0x000000070ba00000, 0x000000070bc00000, 0x000000070bc00000|100%| O|  |TAMS 0x000000070ba00000, 0x000000070ba00000| Untracked 
|  94|0x000000070bc00000, 0x000000070be00000, 0x000000070be00000|100%| O|  |TAMS 0x000000070bc00000, 0x000000070bc00000| Untracked 
|  95|0x000000070be00000, 0x000000070c000000, 0x000000070c000000|100%| O|  |TAMS 0x000000070be00000, 0x000000070be00000| Untracked 
|  96|0x000000070c000000, 0x000000070c200000, 0x000000070c200000|100%| O|  |TAMS 0x000000070c000000, 0x000000070c000000| Untracked 
|  97|0x000000070c200000, 0x000000070c400000, 0x000000070c400000|100%| O|  |TAMS 0x000000070c200000, 0x000000070c200000| Untracked 
|  98|0x000000070c400000, 0x000000070c600000, 0x000000070c600000|100%| O|  |TAMS 0x000000070c400000, 0x000000070c400000| Untracked 
|  99|0x000000070c600000, 0x000000070c800000, 0x000000070c800000|100%| O|  |TAMS 0x000000070c600000, 0x000000070c600000| Untracked 
| 100|0x000000070c800000, 0x000000070ca00000, 0x000000070ca00000|100%| O|  |TAMS 0x000000070c800000, 0x000000070c800000| Untracked 
| 101|0x000000070ca00000, 0x000000070cc00000, 0x000000070cc00000|100%| O|  |TAMS 0x000000070ca00000, 0x000000070ca00000| Untracked 
| 102|0x000000070cc00000, 0x000000070ce00000, 0x000000070ce00000|100%| O|  |TAMS 0x000000070cc00000, 0x000000070cc00000| Untracked 
| 103|0x000000070ce00000, 0x000000070d000000, 0x000000070d000000|100%|HS|  |TAMS 0x000000070d000000, 0x000000070ce00000| Complete 
| 104|0x000000070d000000, 0x000000070d200000, 0x000000070d200000|100%|HC|  |TAMS 0x000000070d200000, 0x000000070d000000| Complete 
| 105|0x000000070d200000, 0x000000070d400000, 0x000000070d400000|100%|HC|  |TAMS 0x000000070d400000, 0x000000070d200000| Complete 
| 106|0x000000070d400000, 0x000000070d600000, 0x000000070d600000|100%|HC|  |TAMS 0x000000070d600000, 0x000000070d400000| Complete 
| 107|0x000000070d600000, 0x000000070d800000, 0x000000070d800000|100%| O|  |TAMS 0x000000070d600000, 0x000000070d600000| Untracked 
| 108|0x000000070d800000, 0x000000070da00000, 0x000000070da00000|100%| O|  |TAMS 0x000000070d800000, 0x000000070d800000| Untracked 
| 109|0x000000070da00000, 0x000000070dc00000, 0x000000070dc00000|100%| O|  |TAMS 0x000000070da00000, 0x000000070da00000| Untracked 
| 110|0x000000070dc00000, 0x000000070de00000, 0x000000070de00000|100%| O|  |TAMS 0x000000070dc00000, 0x000000070dc00000| Untracked 
| 111|0x000000070de00000, 0x000000070e000000, 0x000000070e000000|100%| O|  |TAMS 0x000000070de00000, 0x000000070de00000| Untracked 
| 112|0x000000070e000000, 0x000000070e200000, 0x000000070e200000|100%| O|  |TAMS 0x000000070e000000, 0x000000070e000000| Untracked 
| 113|0x000000070e200000, 0x000000070e400000, 0x000000070e400000|100%| O|  |TAMS 0x000000070e200000, 0x000000070e200000| Untracked 
| 114|0x000000070e400000, 0x000000070e600000, 0x000000070e600000|100%| O|  |TAMS 0x000000070e400000, 0x000000070e400000| Untracked 
| 115|0x000000070e600000, 0x000000070e800000, 0x000000070e800000|100%| O|  |TAMS 0x000000070e600000, 0x000000070e600000| Untracked 
| 116|0x000000070e800000, 0x000000070ea00000, 0x000000070ea00000|100%| O|  |TAMS 0x000000070e800000, 0x000000070e800000| Untracked 
| 117|0x000000070ea00000, 0x000000070ec00000, 0x000000070ec00000|100%| O|  |TAMS 0x000000070ea00000, 0x000000070ea00000| Untracked 
| 118|0x000000070ec00000, 0x000000070ee00000, 0x000000070ee00000|100%| O|  |TAMS 0x000000070ec00000, 0x000000070ec00000| Untracked 
| 119|0x000000070ee00000, 0x000000070f000000, 0x000000070f000000|100%| O|  |TAMS 0x000000070ee00000, 0x000000070ee00000| Untracked 
| 120|0x000000070f000000, 0x000000070f200000, 0x000000070f200000|100%| O|  |TAMS 0x000000070f000000, 0x000000070f000000| Untracked 
| 121|0x000000070f200000, 0x000000070f400000, 0x000000070f400000|100%| O|  |TAMS 0x000000070f200000, 0x000000070f200000| Untracked 
| 122|0x000000070f400000, 0x000000070f600000, 0x000000070f600000|100%| O|  |TAMS 0x000000070f400000, 0x000000070f400000| Untracked 
| 123|0x000000070f600000, 0x000000070f800000, 0x000000070f800000|100%| O|  |TAMS 0x000000070f600000, 0x000000070f600000| Untracked 
| 124|0x000000070f800000, 0x000000070fa00000, 0x000000070fa00000|100%| O|  |TAMS 0x000000070f800000, 0x000000070f800000| Untracked 
| 125|0x000000070fa00000, 0x000000070fc00000, 0x000000070fc00000|100%| O|  |TAMS 0x000000070fa00000, 0x000000070fa00000| Untracked 
| 126|0x000000070fc00000, 0x000000070fe00000, 0x000000070fe00000|100%| O|  |TAMS 0x000000070fc00000, 0x000000070fc00000| Untracked 
| 127|0x000000070fe00000, 0x0000000710000000, 0x0000000710000000|100%| O|  |TAMS 0x000000070fe00000, 0x000000070fe00000| Untracked 
| 128|0x0000000710000000, 0x0000000710200000, 0x0000000710200000|100%| O|  |TAMS 0x0000000710000000, 0x0000000710000000| Untracked 
| 129|0x0000000710200000, 0x0000000710400000, 0x0000000710400000|100%| O|  |TAMS 0x0000000710200000, 0x0000000710200000| Untracked 
| 130|0x0000000710400000, 0x0000000710600000, 0x0000000710600000|100%| O|  |TAMS 0x0000000710400000, 0x0000000710400000| Untracked 
| 131|0x0000000710600000, 0x0000000710800000, 0x0000000710800000|100%| O|  |TAMS 0x0000000710600000, 0x0000000710600000| Untracked 
| 132|0x0000000710800000, 0x0000000710a00000, 0x0000000710a00000|100%| O|  |TAMS 0x0000000710800000, 0x0000000710800000| Untracked 
| 133|0x0000000710a00000, 0x0000000710c00000, 0x0000000710c00000|100%| O|  |TAMS 0x0000000710a00000, 0x0000000710a00000| Untracked 
| 134|0x0000000710c00000, 0x0000000710e00000, 0x0000000710e00000|100%| O|  |TAMS 0x0000000710c00000, 0x0000000710c00000| Untracked 
| 135|0x0000000710e00000, 0x0000000711000000, 0x0000000711000000|100%| O|  |TAMS 0x0000000710e00000, 0x0000000710e00000| Untracked 
| 136|0x0000000711000000, 0x0000000711200000, 0x0000000711200000|100%| O|  |TAMS 0x0000000711000000, 0x0000000711000000| Untracked 
| 137|0x0000000711200000, 0x0000000711400000, 0x0000000711400000|100%| O|  |TAMS 0x0000000711200000, 0x0000000711200000| Untracked 
| 138|0x0000000711400000, 0x0000000711600000, 0x0000000711600000|100%| O|  |TAMS 0x0000000711400000, 0x0000000711400000| Untracked 
| 139|0x0000000711600000, 0x0000000711800000, 0x0000000711800000|100%| O|  |TAMS 0x0000000711600000, 0x0000000711600000| Untracked 
| 140|0x0000000711800000, 0x0000000711a00000, 0x0000000711a00000|100%| O|  |TAMS 0x0000000711800000, 0x0000000711800000| Untracked 
| 141|0x0000000711a00000, 0x0000000711c00000, 0x0000000711c00000|100%| O|  |TAMS 0x0000000711a00000, 0x0000000711a00000| Untracked 
| 142|0x0000000711c00000, 0x0000000711e00000, 0x0000000711e00000|100%| O|  |TAMS 0x0000000711c00000, 0x0000000711c00000| Untracked 
| 143|0x0000000711e00000, 0x0000000712000000, 0x0000000712000000|100%| O|  |TAMS 0x0000000711e00000, 0x0000000711e00000| Untracked 
| 144|0x0000000712000000, 0x0000000712200000, 0x0000000712200000|100%| O|  |TAMS 0x0000000712000000, 0x0000000712000000| Untracked 
| 145|0x0000000712200000, 0x0000000712400000, 0x0000000712400000|100%| O|  |TAMS 0x0000000712200000, 0x0000000712200000| Untracked 
| 146|0x0000000712400000, 0x0000000712600000, 0x0000000712600000|100%| O|  |TAMS 0x0000000712400000, 0x0000000712400000| Untracked 
| 147|0x0000000712600000, 0x0000000712800000, 0x0000000712800000|100%| O|  |TAMS 0x0000000712600000, 0x0000000712600000| Untracked 
| 148|0x0000000712800000, 0x0000000712a00000, 0x0000000712a00000|100%| O|  |TAMS 0x0000000712800000, 0x0000000712800000| Untracked 
| 149|0x0000000712a00000, 0x0000000712c00000, 0x0000000712c00000|100%| O|  |TAMS 0x0000000712a00000, 0x0000000712a00000| Untracked 
| 150|0x0000000712c00000, 0x0000000712e00000, 0x0000000712e00000|100%| O|  |TAMS 0x0000000712c00000, 0x0000000712c00000| Untracked 
| 151|0x0000000712e00000, 0x0000000713000000, 0x0000000713000000|100%| O|  |TAMS 0x0000000712e00000, 0x0000000712e00000| Untracked 
| 152|0x0000000713000000, 0x0000000713200000, 0x0000000713200000|100%| O|  |TAMS 0x0000000713000000, 0x0000000713000000| Untracked 
| 153|0x0000000713200000, 0x0000000713400000, 0x0000000713400000|100%| O|  |TAMS 0x0000000713200000, 0x0000000713200000| Untracked 
| 154|0x0000000713400000, 0x0000000713600000, 0x0000000713600000|100%| O|  |TAMS 0x0000000713400000, 0x0000000713400000| Untracked 
| 155|0x0000000713600000, 0x0000000713800000, 0x0000000713800000|100%| O|  |TAMS 0x0000000713600000, 0x0000000713600000| Untracked 
| 156|0x0000000713800000, 0x0000000713a00000, 0x0000000713a00000|100%| O|  |TAMS 0x0000000713800000, 0x0000000713800000| Untracked 
| 157|0x0000000713a00000, 0x0000000713c00000, 0x0000000713c00000|100%| O|  |TAMS 0x0000000713a00000, 0x0000000713a00000| Untracked 
| 158|0x0000000713c00000, 0x0000000713e00000, 0x0000000713e00000|100%| O|  |TAMS 0x0000000713c00000, 0x0000000713c00000| Untracked 
| 159|0x0000000713e00000, 0x0000000714000000, 0x0000000714000000|100%| O|  |TAMS 0x0000000713e00000, 0x0000000713e00000| Untracked 
| 160|0x0000000714000000, 0x0000000714200000, 0x0000000714200000|100%| O|  |TAMS 0x0000000714000000, 0x0000000714000000| Untracked 
| 161|0x0000000714200000, 0x0000000714400000, 0x0000000714400000|100%| O|  |TAMS 0x0000000714200000, 0x0000000714200000| Untracked 
| 162|0x0000000714400000, 0x0000000714600000, 0x0000000714600000|100%| O|  |TAMS 0x0000000714400000, 0x0000000714400000| Untracked 
| 163|0x0000000714600000, 0x0000000714800000, 0x0000000714800000|100%| O|  |TAMS 0x0000000714600000, 0x0000000714600000| Untracked 
| 164|0x0000000714800000, 0x0000000714a00000, 0x0000000714a00000|100%| O|  |TAMS 0x0000000714800000, 0x0000000714800000| Untracked 
| 165|0x0000000714a00000, 0x0000000714c00000, 0x0000000714c00000|100%| O|  |TAMS 0x0000000714a00000, 0x0000000714a00000| Untracked 
| 166|0x0000000714c00000, 0x0000000714e00000, 0x0000000714e00000|100%| O|  |TAMS 0x0000000714c00000, 0x0000000714c00000| Untracked 
| 167|0x0000000714e00000, 0x0000000715000000, 0x0000000715000000|100%| O|  |TAMS 0x0000000714e00000, 0x0000000714e00000| Untracked 
| 168|0x0000000715000000, 0x0000000715200000, 0x0000000715200000|100%| O|  |TAMS 0x0000000715000000, 0x0000000715000000| Untracked 
| 169|0x0000000715200000, 0x0000000715400000, 0x0000000715400000|100%| O|  |TAMS 0x0000000715200000, 0x0000000715200000| Untracked 
| 170|0x0000000715400000, 0x0000000715600000, 0x0000000715600000|100%| O|  |TAMS 0x0000000715400000, 0x0000000715400000| Untracked 
| 171|0x0000000715600000, 0x0000000715800000, 0x0000000715800000|100%| O|  |TAMS 0x0000000715600000, 0x0000000715600000| Untracked 
| 172|0x0000000715800000, 0x0000000715a00000, 0x0000000715a00000|100%| O|  |TAMS 0x0000000715800000, 0x0000000715800000| Untracked 
| 173|0x0000000715a00000, 0x0000000715c00000, 0x0000000715c00000|100%| O|  |TAMS 0x0000000715a00000, 0x0000000715a00000| Untracked 
| 174|0x0000000715c00000, 0x0000000715e00000, 0x0000000715e00000|100%| O|  |TAMS 0x0000000715c00000, 0x0000000715c00000| Untracked 
| 175|0x0000000715e00000, 0x0000000716000000, 0x0000000716000000|100%| O|  |TAMS 0x0000000715e00000, 0x0000000715e00000| Untracked 
| 176|0x0000000716000000, 0x0000000716200000, 0x0000000716200000|100%| O|  |TAMS 0x0000000716000000, 0x0000000716000000| Untracked 
| 177|0x0000000716200000, 0x0000000716400000, 0x0000000716400000|100%| O|  |TAMS 0x0000000716200000, 0x0000000716200000| Untracked 
| 178|0x0000000716400000, 0x0000000716600000, 0x0000000716600000|100%| O|  |TAMS 0x0000000716400000, 0x0000000716400000| Untracked 
| 179|0x0000000716600000, 0x0000000716800000, 0x0000000716800000|100%| O|  |TAMS 0x0000000716600000, 0x0000000716600000| Untracked 
| 180|0x0000000716800000, 0x0000000716a00000, 0x0000000716a00000|100%| O|  |TAMS 0x0000000716800000, 0x0000000716800000| Untracked 
| 181|0x0000000716a00000, 0x0000000716c00000, 0x0000000716c00000|100%| O|  |TAMS 0x0000000716a00000, 0x0000000716a00000| Untracked 
| 182|0x0000000716c00000, 0x0000000716e00000, 0x0000000716e00000|100%| O|  |TAMS 0x0000000716c00000, 0x0000000716c00000| Untracked 
| 183|0x0000000716e00000, 0x0000000717000000, 0x0000000717000000|100%| O|  |TAMS 0x0000000716e00000, 0x0000000716e00000| Untracked 
| 184|0x0000000717000000, 0x0000000717200000, 0x0000000717200000|100%| O|  |TAMS 0x0000000717000000, 0x0000000717000000| Untracked 
| 185|0x0000000717200000, 0x0000000717400000, 0x0000000717400000|100%| O|  |TAMS 0x0000000717200000, 0x0000000717200000| Untracked 
| 186|0x0000000717400000, 0x0000000717600000, 0x0000000717600000|100%| O|  |TAMS 0x0000000717400000, 0x0000000717400000| Untracked 
| 187|0x0000000717600000, 0x0000000717800000, 0x0000000717800000|100%| O|  |TAMS 0x0000000717600000, 0x0000000717600000| Untracked 
| 188|0x0000000717800000, 0x0000000717a00000, 0x0000000717a00000|100%| O|  |TAMS 0x0000000717800000, 0x0000000717800000| Untracked 
| 189|0x0000000717a00000, 0x0000000717c00000, 0x0000000717c00000|100%| O|  |TAMS 0x0000000717a00000, 0x0000000717a00000| Untracked 
| 190|0x0000000717c00000, 0x0000000717e00000, 0x0000000717e00000|100%| O|  |TAMS 0x0000000717c00000, 0x0000000717c00000| Untracked 
| 191|0x0000000717e00000, 0x0000000718000000, 0x0000000718000000|100%| O|  |TAMS 0x0000000717e00000, 0x0000000717e00000| Untracked 
| 192|0x0000000718000000, 0x0000000718200000, 0x0000000718200000|100%| O|  |TAMS 0x0000000718000000, 0x0000000718000000| Untracked 
| 193|0x0000000718200000, 0x00000007182f2a00, 0x0000000718400000| 47%| O|  |TAMS 0x0000000718200000, 0x0000000718200000| Untracked 
| 194|0x0000000718400000, 0x0000000718400000, 0x0000000718600000|  0%| F|  |TAMS 0x0000000718400000, 0x0000000718400000| Untracked 
| 195|0x0000000718600000, 0x0000000718600000, 0x0000000718800000|  0%| F|  |TAMS 0x0000000718600000, 0x0000000718600000| Untracked 
| 196|0x0000000718800000, 0x0000000718a00000, 0x0000000718a00000|100%| O|  |TAMS 0x0000000718800000, 0x0000000718800000| Untracked 
| 197|0x0000000718a00000, 0x0000000718c00000, 0x0000000718c00000|100%| O|  |TAMS 0x0000000718a00000, 0x0000000718a00000| Untracked 
| 198|0x0000000718c00000, 0x0000000718e00000, 0x0000000718e00000|100%| O|  |TAMS 0x0000000718c00000, 0x0000000718c00000| Untracked 
| 199|0x0000000718e00000, 0x0000000719000000, 0x0000000719000000|100%| O|  |TAMS 0x0000000718e00000, 0x0000000718e00000| Untracked 
| 200|0x0000000719000000, 0x0000000719200000, 0x0000000719200000|100%| O|  |TAMS 0x0000000719000000, 0x0000000719000000| Untracked 
| 201|0x0000000719200000, 0x0000000719400000, 0x0000000719400000|100%| O|  |TAMS 0x0000000719200000, 0x0000000719200000| Untracked 
| 202|0x0000000719400000, 0x0000000719600000, 0x0000000719600000|100%| O|  |TAMS 0x0000000719400000, 0x0000000719400000| Untracked 
| 203|0x0000000719600000, 0x0000000719800000, 0x0000000719800000|100%| O|  |TAMS 0x0000000719600000, 0x0000000719600000| Untracked 
| 204|0x0000000719800000, 0x0000000719a00000, 0x0000000719a00000|100%| O|  |TAMS 0x0000000719800000, 0x0000000719800000| Untracked 
| 205|0x0000000719a00000, 0x0000000719c00000, 0x0000000719c00000|100%| O|  |TAMS 0x0000000719a00000, 0x0000000719a00000| Untracked 
| 206|0x0000000719c00000, 0x0000000719e00000, 0x0000000719e00000|100%| O|  |TAMS 0x0000000719c00000, 0x0000000719c00000| Untracked 
| 207|0x0000000719e00000, 0x000000071a000000, 0x000000071a000000|100%| O|  |TAMS 0x0000000719e00000, 0x0000000719e00000| Untracked 
| 208|0x000000071a000000, 0x000000071a200000, 0x000000071a200000|100%| O|  |TAMS 0x000000071a000000, 0x000000071a000000| Untracked 
| 209|0x000000071a200000, 0x000000071a400000, 0x000000071a400000|100%| O|  |TAMS 0x000000071a200000, 0x000000071a200000| Untracked 
| 210|0x000000071a400000, 0x000000071a600000, 0x000000071a600000|100%| O|  |TAMS 0x000000071a400000, 0x000000071a400000| Untracked 
| 211|0x000000071a600000, 0x000000071a800000, 0x000000071a800000|100%| O|  |TAMS 0x000000071a600000, 0x000000071a600000| Untracked 
| 212|0x000000071a800000, 0x000000071aa00000, 0x000000071aa00000|100%| O|  |TAMS 0x000000071a800000, 0x000000071a800000| Untracked 
| 213|0x000000071aa00000, 0x000000071ac00000, 0x000000071ac00000|100%| O|  |TAMS 0x000000071aa00000, 0x000000071aa00000| Untracked 
| 214|0x000000071ac00000, 0x000000071ac00000, 0x000000071ae00000|  0%| F|  |TAMS 0x000000071ac00000, 0x000000071ac00000| Untracked 
| 215|0x000000071ae00000, 0x000000071ae00000, 0x000000071b000000|  0%| F|  |TAMS 0x000000071ae00000, 0x000000071ae00000| Untracked 
| 216|0x000000071b000000, 0x000000071b000000, 0x000000071b200000|  0%| F|  |TAMS 0x000000071b000000, 0x000000071b000000| Untracked 
| 217|0x000000071b200000, 0x000000071b200000, 0x000000071b400000|  0%| F|  |TAMS 0x000000071b200000, 0x000000071b200000| Untracked 
| 218|0x000000071b400000, 0x000000071b400000, 0x000000071b600000|  0%| F|  |TAMS 0x000000071b400000, 0x000000071b400000| Untracked 
| 219|0x000000071b600000, 0x000000071b600000, 0x000000071b800000|  0%| F|  |TAMS 0x000000071b600000, 0x000000071b600000| Untracked 
| 220|0x000000071b800000, 0x000000071b800000, 0x000000071ba00000|  0%| F|  |TAMS 0x000000071b800000, 0x000000071b800000| Untracked 
| 221|0x000000071ba00000, 0x000000071ba00000, 0x000000071bc00000|  0%| F|  |TAMS 0x000000071ba00000, 0x000000071ba00000| Untracked 
| 222|0x000000071bc00000, 0x000000071bc00000, 0x000000071be00000|  0%| F|  |TAMS 0x000000071bc00000, 0x000000071bc00000| Untracked 
| 223|0x000000071be00000, 0x000000071be00000, 0x000000071c000000|  0%| F|  |TAMS 0x000000071be00000, 0x000000071be00000| Untracked 
| 224|0x000000071c000000, 0x000000071c000000, 0x000000071c200000|  0%| F|  |TAMS 0x000000071c000000, 0x000000071c000000| Untracked 
| 225|0x000000071c200000, 0x000000071c200000, 0x000000071c400000|  0%| F|  |TAMS 0x000000071c200000, 0x000000071c200000| Untracked 
| 226|0x000000071c400000, 0x000000071c400000, 0x000000071c600000|  0%| F|  |TAMS 0x000000071c400000, 0x000000071c400000| Untracked 
| 227|0x000000071c600000, 0x000000071c600000, 0x000000071c800000|  0%| F|  |TAMS 0x000000071c600000, 0x000000071c600000| Untracked 
| 228|0x000000071c800000, 0x000000071c800000, 0x000000071ca00000|  0%| F|  |TAMS 0x000000071c800000, 0x000000071c800000| Untracked 
| 229|0x000000071ca00000, 0x000000071ca00000, 0x000000071cc00000|  0%| F|  |TAMS 0x000000071ca00000, 0x000000071ca00000| Untracked 
| 230|0x000000071cc00000, 0x000000071cc00000, 0x000000071ce00000|  0%| F|  |TAMS 0x000000071cc00000, 0x000000071cc00000| Untracked 
| 231|0x000000071ce00000, 0x000000071ce00000, 0x000000071d000000|  0%| F|  |TAMS 0x000000071ce00000, 0x000000071ce00000| Untracked 
| 232|0x000000071d000000, 0x000000071d000000, 0x000000071d200000|  0%| F|  |TAMS 0x000000071d000000, 0x000000071d000000| Untracked 
| 233|0x000000071d200000, 0x000000071d200000, 0x000000071d400000|  0%| F|  |TAMS 0x000000071d200000, 0x000000071d200000| Untracked 
| 234|0x000000071d400000, 0x000000071d400000, 0x000000071d600000|  0%| F|  |TAMS 0x000000071d400000, 0x000000071d400000| Untracked 
| 235|0x000000071d600000, 0x000000071d600000, 0x000000071d800000|  0%| F|  |TAMS 0x000000071d600000, 0x000000071d600000| Untracked 
| 236|0x000000071d800000, 0x000000071d800000, 0x000000071da00000|  0%| F|  |TAMS 0x000000071d800000, 0x000000071d800000| Untracked 
| 237|0x000000071da00000, 0x000000071da00000, 0x000000071dc00000|  0%| F|  |TAMS 0x000000071da00000, 0x000000071da00000| Untracked 
| 238|0x000000071dc00000, 0x000000071dc00000, 0x000000071de00000|  0%| F|  |TAMS 0x000000071dc00000, 0x000000071dc00000| Untracked 
| 239|0x000000071de00000, 0x000000071de00000, 0x000000071e000000|  0%| F|  |TAMS 0x000000071de00000, 0x000000071de00000| Untracked 
| 240|0x000000071e000000, 0x000000071e000000, 0x000000071e200000|  0%| F|  |TAMS 0x000000071e000000, 0x000000071e000000| Untracked 
| 241|0x000000071e200000, 0x000000071e200000, 0x000000071e400000|  0%| F|  |TAMS 0x000000071e200000, 0x000000071e200000| Untracked 
| 242|0x000000071e400000, 0x000000071e400000, 0x000000071e600000|  0%| F|  |TAMS 0x000000071e400000, 0x000000071e400000| Untracked 
| 243|0x000000071e600000, 0x000000071e600000, 0x000000071e800000|  0%| F|  |TAMS 0x000000071e600000, 0x000000071e600000| Untracked 
| 244|0x000000071e800000, 0x000000071e800000, 0x000000071ea00000|  0%| F|  |TAMS 0x000000071e800000, 0x000000071e800000| Untracked 
| 245|0x000000071ea00000, 0x000000071ea00000, 0x000000071ec00000|  0%| F|  |TAMS 0x000000071ea00000, 0x000000071ea00000| Untracked 
| 246|0x000000071ec00000, 0x000000071ec00000, 0x000000071ee00000|  0%| F|  |TAMS 0x000000071ec00000, 0x000000071ec00000| Untracked 
| 247|0x000000071ee00000, 0x000000071ee00000, 0x000000071f000000|  0%| F|  |TAMS 0x000000071ee00000, 0x000000071ee00000| Untracked 
| 248|0x000000071f000000, 0x000000071f000000, 0x000000071f200000|  0%| F|  |TAMS 0x000000071f000000, 0x000000071f000000| Untracked 
| 249|0x000000071f200000, 0x000000071f200000, 0x000000071f400000|  0%| F|  |TAMS 0x000000071f200000, 0x000000071f200000| Untracked 
| 250|0x000000071f400000, 0x000000071f400000, 0x000000071f600000|  0%| F|  |TAMS 0x000000071f400000, 0x000000071f400000| Untracked 
| 251|0x000000071f600000, 0x000000071f600000, 0x000000071f800000|  0%| F|  |TAMS 0x000000071f600000, 0x000000071f600000| Untracked 
| 252|0x000000071f800000, 0x000000071f800000, 0x000000071fa00000|  0%| F|  |TAMS 0x000000071f800000, 0x000000071f800000| Untracked 
| 253|0x000000071fa00000, 0x000000071fa00000, 0x000000071fc00000|  0%| F|  |TAMS 0x000000071fa00000, 0x000000071fa00000| Untracked 
| 254|0x000000071fc00000, 0x000000071fc00000, 0x000000071fe00000|  0%| F|  |TAMS 0x000000071fc00000, 0x000000071fc00000| Untracked 
| 255|0x000000071fe00000, 0x000000071fe00000, 0x0000000720000000|  0%| F|  |TAMS 0x000000071fe00000, 0x000000071fe00000| Untracked 
| 256|0x0000000720000000, 0x0000000720000000, 0x0000000720200000|  0%| F|  |TAMS 0x0000000720000000, 0x0000000720000000| Untracked 
| 257|0x0000000720200000, 0x0000000720200000, 0x0000000720400000|  0%| F|  |TAMS 0x0000000720200000, 0x0000000720200000| Untracked 
| 258|0x0000000720400000, 0x0000000720400000, 0x0000000720600000|  0%| F|  |TAMS 0x0000000720400000, 0x0000000720400000| Untracked 
| 259|0x0000000720600000, 0x0000000720600000, 0x0000000720800000|  0%| F|  |TAMS 0x0000000720600000, 0x0000000720600000| Untracked 
| 260|0x0000000720800000, 0x0000000720800000, 0x0000000720a00000|  0%| F|  |TAMS 0x0000000720800000, 0x0000000720800000| Untracked 
| 261|0x0000000720a00000, 0x0000000720a00000, 0x0000000720c00000|  0%| F|  |TAMS 0x0000000720a00000, 0x0000000720a00000| Untracked 
| 262|0x0000000720c00000, 0x0000000720c00000, 0x0000000720e00000|  0%| F|  |TAMS 0x0000000720c00000, 0x0000000720c00000| Untracked 
| 263|0x0000000720e00000, 0x0000000720e00000, 0x0000000721000000|  0%| F|  |TAMS 0x0000000720e00000, 0x0000000720e00000| Untracked 
| 264|0x0000000721000000, 0x0000000721000000, 0x0000000721200000|  0%| F|  |TAMS 0x0000000721000000, 0x0000000721000000| Untracked 
| 265|0x0000000721200000, 0x0000000721200000, 0x0000000721400000|  0%| F|  |TAMS 0x0000000721200000, 0x0000000721200000| Untracked 
| 266|0x0000000721400000, 0x0000000721400000, 0x0000000721600000|  0%| F|  |TAMS 0x0000000721400000, 0x0000000721400000| Untracked 
| 267|0x0000000721600000, 0x0000000721600000, 0x0000000721800000|  0%| F|  |TAMS 0x0000000721600000, 0x0000000721600000| Untracked 
| 268|0x0000000721800000, 0x0000000721800000, 0x0000000721a00000|  0%| F|  |TAMS 0x0000000721800000, 0x0000000721800000| Untracked 
| 269|0x0000000721a00000, 0x0000000721c00000, 0x0000000721c00000|100%| S|CS|TAMS 0x0000000721a00000, 0x0000000721a00000| Complete 
| 270|0x0000000721c00000, 0x0000000721e00000, 0x0000000721e00000|100%| S|CS|TAMS 0x0000000721c00000, 0x0000000721c00000| Complete 
| 271|0x0000000721e00000, 0x0000000722000000, 0x0000000722000000|100%| S|CS|TAMS 0x0000000721e00000, 0x0000000721e00000| Complete 
| 272|0x0000000722000000, 0x0000000722200000, 0x0000000722200000|100%| S|CS|TAMS 0x0000000722000000, 0x0000000722000000| Complete 
| 273|0x0000000722200000, 0x0000000722400000, 0x0000000722400000|100%| S|CS|TAMS 0x0000000722200000, 0x0000000722200000| Complete 
| 274|0x0000000722400000, 0x0000000722600000, 0x0000000722600000|100%| S|CS|TAMS 0x0000000722400000, 0x0000000722400000| Complete 
| 275|0x0000000722600000, 0x0000000722800000, 0x0000000722800000|100%| S|CS|TAMS 0x0000000722600000, 0x0000000722600000| Complete 
| 276|0x0000000722800000, 0x0000000722a00000, 0x0000000722a00000|100%| S|CS|TAMS 0x0000000722800000, 0x0000000722800000| Complete 
| 277|0x0000000722a00000, 0x0000000722c00000, 0x0000000722c00000|100%| S|CS|TAMS 0x0000000722a00000, 0x0000000722a00000| Complete 
| 278|0x0000000722c00000, 0x0000000722e00000, 0x0000000722e00000|100%| S|CS|TAMS 0x0000000722c00000, 0x0000000722c00000| Complete 
| 279|0x0000000722e00000, 0x0000000723000000, 0x0000000723000000|100%| S|CS|TAMS 0x0000000722e00000, 0x0000000722e00000| Complete 
| 280|0x0000000723000000, 0x0000000723200000, 0x0000000723200000|100%| S|CS|TAMS 0x0000000723000000, 0x0000000723000000| Complete 
| 281|0x0000000723200000, 0x0000000723400000, 0x0000000723400000|100%| S|CS|TAMS 0x0000000723200000, 0x0000000723200000| Complete 
| 282|0x0000000723400000, 0x0000000723400000, 0x0000000723600000|  0%| F|  |TAMS 0x0000000723400000, 0x0000000723400000| Untracked 
| 283|0x0000000723600000, 0x0000000723600000, 0x0000000723800000|  0%| F|  |TAMS 0x0000000723600000, 0x0000000723600000| Untracked 
| 284|0x0000000723800000, 0x0000000723800000, 0x0000000723a00000|  0%| F|  |TAMS 0x0000000723800000, 0x0000000723800000| Untracked 
| 285|0x0000000723a00000, 0x0000000723a00000, 0x0000000723c00000|  0%| F|  |TAMS 0x0000000723a00000, 0x0000000723a00000| Untracked 
| 286|0x0000000723c00000, 0x0000000723c00000, 0x0000000723e00000|  0%| F|  |TAMS 0x0000000723c00000, 0x0000000723c00000| Untracked 
| 287|0x0000000723e00000, 0x0000000723e00000, 0x0000000724000000|  0%| F|  |TAMS 0x0000000723e00000, 0x0000000723e00000| Untracked 
| 288|0x0000000724000000, 0x0000000724000000, 0x0000000724200000|  0%| F|  |TAMS 0x0000000724000000, 0x0000000724000000| Untracked 
| 289|0x0000000724200000, 0x0000000724200000, 0x0000000724400000|  0%| F|  |TAMS 0x0000000724200000, 0x0000000724200000| Untracked 
| 290|0x0000000724400000, 0x0000000724400000, 0x0000000724600000|  0%| F|  |TAMS 0x0000000724400000, 0x0000000724400000| Untracked 
| 291|0x0000000724600000, 0x0000000724600000, 0x0000000724800000|  0%| F|  |TAMS 0x0000000724600000, 0x0000000724600000| Untracked 
| 292|0x0000000724800000, 0x0000000724800000, 0x0000000724a00000|  0%| F|  |TAMS 0x0000000724800000, 0x0000000724800000| Untracked 
| 293|0x0000000724a00000, 0x0000000724a00000, 0x0000000724c00000|  0%| F|  |TAMS 0x0000000724a00000, 0x0000000724a00000| Untracked 
| 294|0x0000000724c00000, 0x0000000724c00000, 0x0000000724e00000|  0%| F|  |TAMS 0x0000000724c00000, 0x0000000724c00000| Untracked 
| 295|0x0000000724e00000, 0x0000000724e00000, 0x0000000725000000|  0%| F|  |TAMS 0x0000000724e00000, 0x0000000724e00000| Untracked 
| 296|0x0000000725000000, 0x0000000725000000, 0x0000000725200000|  0%| F|  |TAMS 0x0000000725000000, 0x0000000725000000| Untracked 
| 297|0x0000000725200000, 0x0000000725200000, 0x0000000725400000|  0%| F|  |TAMS 0x0000000725200000, 0x0000000725200000| Untracked 
| 298|0x0000000725400000, 0x0000000725400000, 0x0000000725600000|  0%| F|  |TAMS 0x0000000725400000, 0x0000000725400000| Untracked 
| 299|0x0000000725600000, 0x0000000725600000, 0x0000000725800000|  0%| F|  |TAMS 0x0000000725600000, 0x0000000725600000| Untracked 
| 300|0x0000000725800000, 0x0000000725800000, 0x0000000725a00000|  0%| F|  |TAMS 0x0000000725800000, 0x0000000725800000| Untracked 
| 301|0x0000000725a00000, 0x0000000725a00000, 0x0000000725c00000|  0%| F|  |TAMS 0x0000000725a00000, 0x0000000725a00000| Untracked 
| 302|0x0000000725c00000, 0x0000000725c00000, 0x0000000725e00000|  0%| F|  |TAMS 0x0000000725c00000, 0x0000000725c00000| Untracked 
| 303|0x0000000725e00000, 0x0000000725e00000, 0x0000000726000000|  0%| F|  |TAMS 0x0000000725e00000, 0x0000000725e00000| Untracked 
| 304|0x0000000726000000, 0x0000000726000000, 0x0000000726200000|  0%| F|  |TAMS 0x0000000726000000, 0x0000000726000000| Untracked 
| 305|0x0000000726200000, 0x0000000726200000, 0x0000000726400000|  0%| F|  |TAMS 0x0000000726200000, 0x0000000726200000| Untracked 
| 306|0x0000000726400000, 0x0000000726400000, 0x0000000726600000|  0%| F|  |TAMS 0x0000000726400000, 0x0000000726400000| Untracked 
| 307|0x0000000726600000, 0x0000000726600000, 0x0000000726800000|  0%| F|  |TAMS 0x0000000726600000, 0x0000000726600000| Untracked 
| 308|0x0000000726800000, 0x0000000726800000, 0x0000000726a00000|  0%| F|  |TAMS 0x0000000726800000, 0x0000000726800000| Untracked 
| 309|0x0000000726a00000, 0x0000000726a00000, 0x0000000726c00000|  0%| F|  |TAMS 0x0000000726a00000, 0x0000000726a00000| Untracked 
| 310|0x0000000726c00000, 0x0000000726c00000, 0x0000000726e00000|  0%| F|  |TAMS 0x0000000726c00000, 0x0000000726c00000| Untracked 
| 311|0x0000000726e00000, 0x0000000726e00000, 0x0000000727000000|  0%| F|  |TAMS 0x0000000726e00000, 0x0000000726e00000| Untracked 
| 312|0x0000000727000000, 0x0000000727000000, 0x0000000727200000|  0%| F|  |TAMS 0x0000000727000000, 0x0000000727000000| Untracked 
| 313|0x0000000727200000, 0x0000000727200000, 0x0000000727400000|  0%| F|  |TAMS 0x0000000727200000, 0x0000000727200000| Untracked 
| 314|0x0000000727400000, 0x0000000727400000, 0x0000000727600000|  0%| F|  |TAMS 0x0000000727400000, 0x0000000727400000| Untracked 
| 315|0x0000000727600000, 0x0000000727600000, 0x0000000727800000|  0%| F|  |TAMS 0x0000000727600000, 0x0000000727600000| Untracked 
| 316|0x0000000727800000, 0x0000000727800000, 0x0000000727a00000|  0%| F|  |TAMS 0x0000000727800000, 0x0000000727800000| Untracked 
| 317|0x0000000727a00000, 0x0000000727a00000, 0x0000000727c00000|  0%| F|  |TAMS 0x0000000727a00000, 0x0000000727a00000| Untracked 
| 318|0x0000000727c00000, 0x0000000727c00000, 0x0000000727e00000|  0%| F|  |TAMS 0x0000000727c00000, 0x0000000727c00000| Untracked 
| 319|0x0000000727e00000, 0x0000000727e00000, 0x0000000728000000|  0%| F|  |TAMS 0x0000000727e00000, 0x0000000727e00000| Untracked 
| 320|0x0000000728000000, 0x0000000728000000, 0x0000000728200000|  0%| F|  |TAMS 0x0000000728000000, 0x0000000728000000| Untracked 
| 321|0x0000000728200000, 0x0000000728200000, 0x0000000728400000|  0%| F|  |TAMS 0x0000000728200000, 0x0000000728200000| Untracked 
| 322|0x0000000728400000, 0x0000000728400000, 0x0000000728600000|  0%| F|  |TAMS 0x0000000728400000, 0x0000000728400000| Untracked 
| 323|0x0000000728600000, 0x0000000728600000, 0x0000000728800000|  0%| F|  |TAMS 0x0000000728600000, 0x0000000728600000| Untracked 
| 324|0x0000000728800000, 0x0000000728800000, 0x0000000728a00000|  0%| F|  |TAMS 0x0000000728800000, 0x0000000728800000| Untracked 
| 325|0x0000000728a00000, 0x0000000728a00000, 0x0000000728c00000|  0%| F|  |TAMS 0x0000000728a00000, 0x0000000728a00000| Untracked 
| 326|0x0000000728c00000, 0x0000000728c00000, 0x0000000728e00000|  0%| F|  |TAMS 0x0000000728c00000, 0x0000000728c00000| Untracked 
| 327|0x0000000728e00000, 0x0000000728e00000, 0x0000000729000000|  0%| F|  |TAMS 0x0000000728e00000, 0x0000000728e00000| Untracked 
| 328|0x0000000729000000, 0x0000000729000000, 0x0000000729200000|  0%| F|  |TAMS 0x0000000729000000, 0x0000000729000000| Untracked 
| 329|0x0000000729200000, 0x0000000729200000, 0x0000000729400000|  0%| F|  |TAMS 0x0000000729200000, 0x0000000729200000| Untracked 
| 330|0x0000000729400000, 0x0000000729400000, 0x0000000729600000|  0%| F|  |TAMS 0x0000000729400000, 0x0000000729400000| Untracked 
| 331|0x0000000729600000, 0x0000000729600000, 0x0000000729800000|  0%| F|  |TAMS 0x0000000729600000, 0x0000000729600000| Untracked 
| 332|0x0000000729800000, 0x0000000729800000, 0x0000000729a00000|  0%| F|  |TAMS 0x0000000729800000, 0x0000000729800000| Untracked 
| 333|0x0000000729a00000, 0x0000000729a00000, 0x0000000729c00000|  0%| F|  |TAMS 0x0000000729a00000, 0x0000000729a00000| Untracked 
| 334|0x0000000729c00000, 0x0000000729c00000, 0x0000000729e00000|  0%| F|  |TAMS 0x0000000729c00000, 0x0000000729c00000| Untracked 
| 335|0x0000000729e00000, 0x0000000729e00000, 0x000000072a000000|  0%| F|  |TAMS 0x0000000729e00000, 0x0000000729e00000| Untracked 
| 336|0x000000072a000000, 0x000000072a000000, 0x000000072a200000|  0%| F|  |TAMS 0x000000072a000000, 0x000000072a000000| Untracked 
| 337|0x000000072a200000, 0x000000072a200000, 0x000000072a400000|  0%| F|  |TAMS 0x000000072a200000, 0x000000072a200000| Untracked 
| 338|0x000000072a400000, 0x000000072a400000, 0x000000072a600000|  0%| F|  |TAMS 0x000000072a400000, 0x000000072a400000| Untracked 
| 339|0x000000072a600000, 0x000000072a600000, 0x000000072a800000|  0%| F|  |TAMS 0x000000072a600000, 0x000000072a600000| Untracked 
| 340|0x000000072a800000, 0x000000072a800000, 0x000000072aa00000|  0%| F|  |TAMS 0x000000072a800000, 0x000000072a800000| Untracked 
| 341|0x000000072aa00000, 0x000000072aa00000, 0x000000072ac00000|  0%| F|  |TAMS 0x000000072aa00000, 0x000000072aa00000| Untracked 
| 342|0x000000072ac00000, 0x000000072ac00000, 0x000000072ae00000|  0%| F|  |TAMS 0x000000072ac00000, 0x000000072ac00000| Untracked 
| 343|0x000000072ae00000, 0x000000072ae00000, 0x000000072b000000|  0%| F|  |TAMS 0x000000072ae00000, 0x000000072ae00000| Untracked 
| 344|0x000000072b000000, 0x000000072b000000, 0x000000072b200000|  0%| F|  |TAMS 0x000000072b000000, 0x000000072b000000| Untracked 
| 345|0x000000072b200000, 0x000000072b200000, 0x000000072b400000|  0%| F|  |TAMS 0x000000072b200000, 0x000000072b200000| Untracked 
| 346|0x000000072b400000, 0x000000072b400000, 0x000000072b600000|  0%| F|  |TAMS 0x000000072b400000, 0x000000072b400000| Untracked 
| 347|0x000000072b600000, 0x000000072b600000, 0x000000072b800000|  0%| F|  |TAMS 0x000000072b600000, 0x000000072b600000| Untracked 
| 348|0x000000072b800000, 0x000000072b800000, 0x000000072ba00000|  0%| F|  |TAMS 0x000000072b800000, 0x000000072b800000| Untracked 
| 349|0x000000072ba00000, 0x000000072ba00000, 0x000000072bc00000|  0%| F|  |TAMS 0x000000072ba00000, 0x000000072ba00000| Untracked 
| 350|0x000000072bc00000, 0x000000072bc00000, 0x000000072be00000|  0%| F|  |TAMS 0x000000072bc00000, 0x000000072bc00000| Untracked 
| 351|0x000000072be00000, 0x000000072be00000, 0x000000072c000000|  0%| F|  |TAMS 0x000000072be00000, 0x000000072be00000| Untracked 
| 352|0x000000072c000000, 0x000000072c000000, 0x000000072c200000|  0%| F|  |TAMS 0x000000072c000000, 0x000000072c000000| Untracked 
| 353|0x000000072c200000, 0x000000072c200000, 0x000000072c400000|  0%| F|  |TAMS 0x000000072c200000, 0x000000072c200000| Untracked 
| 354|0x000000072c400000, 0x000000072c400000, 0x000000072c600000|  0%| F|  |TAMS 0x000000072c400000, 0x000000072c400000| Untracked 
| 355|0x000000072c600000, 0x000000072c600000, 0x000000072c800000|  0%| F|  |TAMS 0x000000072c600000, 0x000000072c600000| Untracked 
| 356|0x000000072c800000, 0x000000072c800000, 0x000000072ca00000|  0%| F|  |TAMS 0x000000072c800000, 0x000000072c800000| Untracked 
| 357|0x000000072ca00000, 0x000000072ca00000, 0x000000072cc00000|  0%| F|  |TAMS 0x000000072ca00000, 0x000000072ca00000| Untracked 
| 358|0x000000072cc00000, 0x000000072cc00000, 0x000000072ce00000|  0%| F|  |TAMS 0x000000072cc00000, 0x000000072cc00000| Untracked 
| 359|0x000000072ce00000, 0x000000072ce00000, 0x000000072d000000|  0%| F|  |TAMS 0x000000072ce00000, 0x000000072ce00000| Untracked 
| 360|0x000000072d000000, 0x000000072d000000, 0x000000072d200000|  0%| F|  |TAMS 0x000000072d000000, 0x000000072d000000| Untracked 
| 361|0x000000072d200000, 0x000000072d200000, 0x000000072d400000|  0%| F|  |TAMS 0x000000072d200000, 0x000000072d200000| Untracked 
| 362|0x000000072d400000, 0x000000072d400000, 0x000000072d600000|  0%| F|  |TAMS 0x000000072d400000, 0x000000072d400000| Untracked 
| 363|0x000000072d600000, 0x000000072d600000, 0x000000072d800000|  0%| F|  |TAMS 0x000000072d600000, 0x000000072d600000| Untracked 
| 364|0x000000072d800000, 0x000000072d800000, 0x000000072da00000|  0%| F|  |TAMS 0x000000072d800000, 0x000000072d800000| Untracked 
| 365|0x000000072da00000, 0x000000072da00000, 0x000000072dc00000|  0%| F|  |TAMS 0x000000072da00000, 0x000000072da00000| Untracked 
| 366|0x000000072dc00000, 0x000000072dc00000, 0x000000072de00000|  0%| F|  |TAMS 0x000000072dc00000, 0x000000072dc00000| Untracked 
| 367|0x000000072de00000, 0x000000072de00000, 0x000000072e000000|  0%| F|  |TAMS 0x000000072de00000, 0x000000072de00000| Untracked 
| 368|0x000000072e000000, 0x000000072e000000, 0x000000072e200000|  0%| F|  |TAMS 0x000000072e000000, 0x000000072e000000| Untracked 
| 369|0x000000072e200000, 0x000000072e200000, 0x000000072e400000|  0%| F|  |TAMS 0x000000072e200000, 0x000000072e200000| Untracked 
| 370|0x000000072e400000, 0x000000072e400000, 0x000000072e600000|  0%| F|  |TAMS 0x000000072e400000, 0x000000072e400000| Untracked 
| 371|0x000000072e600000, 0x000000072e600000, 0x000000072e800000|  0%| F|  |TAMS 0x000000072e600000, 0x000000072e600000| Untracked 
| 372|0x000000072e800000, 0x000000072e800000, 0x000000072ea00000|  0%| F|  |TAMS 0x000000072e800000, 0x000000072e800000| Untracked 
| 373|0x000000072ea00000, 0x000000072ea00000, 0x000000072ec00000|  0%| F|  |TAMS 0x000000072ea00000, 0x000000072ea00000| Untracked 
| 374|0x000000072ec00000, 0x000000072ec00000, 0x000000072ee00000|  0%| F|  |TAMS 0x000000072ec00000, 0x000000072ec00000| Untracked 
| 375|0x000000072ee00000, 0x000000072ee00000, 0x000000072f000000|  0%| F|  |TAMS 0x000000072ee00000, 0x000000072ee00000| Untracked 
| 376|0x000000072f000000, 0x000000072f000000, 0x000000072f200000|  0%| F|  |TAMS 0x000000072f000000, 0x000000072f000000| Untracked 
| 377|0x000000072f200000, 0x000000072f200000, 0x000000072f400000|  0%| F|  |TAMS 0x000000072f200000, 0x000000072f200000| Untracked 
| 378|0x000000072f400000, 0x000000072f400000, 0x000000072f600000|  0%| F|  |TAMS 0x000000072f400000, 0x000000072f400000| Untracked 
| 379|0x000000072f600000, 0x000000072f600000, 0x000000072f800000|  0%| F|  |TAMS 0x000000072f600000, 0x000000072f600000| Untracked 
| 380|0x000000072f800000, 0x000000072f800000, 0x000000072fa00000|  0%| F|  |TAMS 0x000000072f800000, 0x000000072f800000| Untracked 
| 381|0x000000072fa00000, 0x000000072fa00000, 0x000000072fc00000|  0%| F|  |TAMS 0x000000072fa00000, 0x000000072fa00000| Untracked 
| 382|0x000000072fc00000, 0x000000072fc00000, 0x000000072fe00000|  0%| F|  |TAMS 0x000000072fc00000, 0x000000072fc00000| Untracked 
| 383|0x000000072fe00000, 0x000000072fe00000, 0x0000000730000000|  0%| F|  |TAMS 0x000000072fe00000, 0x000000072fe00000| Untracked 
| 384|0x0000000730000000, 0x0000000730000000, 0x0000000730200000|  0%| F|  |TAMS 0x0000000730000000, 0x0000000730000000| Untracked 
| 385|0x0000000730200000, 0x0000000730200000, 0x0000000730400000|  0%| F|  |TAMS 0x0000000730200000, 0x0000000730200000| Untracked 
| 386|0x0000000730400000, 0x0000000730400000, 0x0000000730600000|  0%| F|  |TAMS 0x0000000730400000, 0x0000000730400000| Untracked 

Card table byte_map: [0x000001a52cf50000,0x000001a52d750000] _byte_map_base: 0x000001a529750000

Marking Bits (Prev, Next): (CMBitMap*) 0x000001a514ea03b0, (CMBitMap*) 0x000001a514ea0370
 Prev Bits: [0x000001a531f50000, 0x000001a535f50000)
 Next Bits: [0x000001a52df50000, 0x000001a531f50000)

Polling page: 0x000001a5135c0000

Metaspace:

Usage:
  Non-class:    128.29 MB used.
      Class:     18.70 MB used.
       Both:    146.99 MB used.

Virtual space:
  Non-class space:      192.00 MB reserved,     128.81 MB ( 67%) committed,  3 nodes.
      Class space:        1.00 GB reserved,      19.25 MB (  2%) committed,  1 nodes.
             Both:        1.19 GB reserved,     148.06 MB ( 12%) committed. 

Chunk freelists:
   Non-Class:  14.51 MB
       Class:  12.67 MB
        Both:  27.18 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 229.50 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 12.
num_arena_births: 1666.
num_arena_deaths: 18.
num_vsnodes_births: 4.
num_vsnodes_deaths: 0.
num_space_committed: 2367.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 68.
num_chunks_taken_from_freelist: 7938.
num_chunk_merges: 20.
num_chunk_splits: 5266.
num_chunks_enlarged: 3522.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=28495Kb max_used=28523Kb free=91504Kb
 bounds [0x000001a524590000, 0x000001a526190000, 0x000001a52bac0000]
CodeHeap 'profiled nmethods': size=120000Kb used=63034Kb max_used=63034Kb free=56965Kb
 bounds [0x000001a51cac0000, 0x000001a520850000, 0x000001a523ff0000]
CodeHeap 'non-nmethods': size=5760Kb used=2514Kb max_used=2597Kb free=3245Kb
 bounds [0x000001a523ff0000, 0x000001a524280000, 0x000001a524590000]
 total_blobs=32271 nmethods=31212 adapters=966
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 252.068 Thread 0x000001a538472000 nmethod 43741 0x000001a525f64510 code [0x000001a525f646a0, 0x000001a525f64778]
Event: 252.068 Thread 0x000001a538472000 43742       3       com.android.tools.r8.internal.U10::a (402 bytes)
Event: 252.069 Thread 0x000001a538472000 nmethod 43742 0x000001a520828390 code [0x000001a5208287a0, 0x000001a52082b008]
Event: 252.069 Thread 0x000001a538472000 43743       2       com.android.tools.r8.graph.N3::a (23 bytes)
Event: 252.069 Thread 0x000001a538472000 nmethod 43743 0x000001a52082b790 code [0x000001a52082b940, 0x000001a52082bc98]
Event: 252.069 Thread 0x000001a538472000 43744       2       com.android.tools.r8.graph.N3$$Lambda$3823/0x000001a53b1e0688::<init> (20 bytes)
Event: 252.070 Thread 0x000001a538472000 nmethod 43744 0x000001a52082be10 code [0x000001a52082bfa0, 0x000001a52082c1d8]
Event: 252.070 Thread 0x000001a538472000 43745       2       com.android.tools.r8.graph.N3$$Lambda$3823/0x000001a53b1e0688::accept (24 bytes)
Event: 252.070 Thread 0x000001a538472000 nmethod 43745 0x000001a52082c290 code [0x000001a52082c440, 0x000001a52082c648]
Event: 252.070 Thread 0x000001a538472000 43746       2       com.android.tools.r8.graph.N3::a (25 bytes)
Event: 252.070 Thread 0x000001a538472000 nmethod 43746 0x000001a52082c790 code [0x000001a52082c940, 0x000001a52082cb98]
Event: 252.071 Thread 0x000001a538472000 43747       3       com.android.tools.r8.internal.o7::a (1671 bytes)
Event: 252.082 Thread 0x000001a538472000 nmethod 43747 0x000001a52082cc90 code [0x000001a52082e100, 0x000001a52083ddf8]
Event: 252.082 Thread 0x000001a538472000 43758       3       com.android.tools.r8.internal.hl::b (624 bytes)
Event: 252.084 Thread 0x000001a538472000 nmethod 43758 0x000001a520842a10 code [0x000001a520842f40, 0x000001a5208459d8]
Event: 252.084 Thread 0x000001a538472000 43761       3       com.android.tools.r8.internal.NL::a (299 bytes)
Event: 252.086 Thread 0x000001a538472000 nmethod 43761 0x000001a520846b90 code [0x000001a520846fe0, 0x000001a5208496f8]
Event: 252.086 Thread 0x000001a538472000 43764       2       com.android.tools.r8.ir.optimize.F::a (843 bytes)
Event: 252.089 Thread 0x000001a538472000 nmethod 43764 0x000001a52084a490 code [0x000001a52084ab00, 0x000001a52084d178]
Event: 252.089 Thread 0x000001a538472000 43748       3       com.android.tools.r8.internal.hl::a (2961 bytes)

GC Heap History (20 events):
Event: 244.327 GC heap after
{Heap after GC invocations=74 (full 0):
 garbage-first heap   total 792576K, used 193443K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 4 young (8192K), 4 survivors (8192K)
 Metaspace       used 139080K, committed 140096K, reserved 1179648K
  class space    used 17896K, committed 18432K, reserved 1048576K
}
Event: 247.014 GC heap before
{Heap before GC invocations=74 (full 0):
 garbage-first heap   total 792576K, used 656291K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 230 young (471040K), 4 survivors (8192K)
 Metaspace       used 139637K, committed 140672K, reserved 1179648K
  class space    used 17944K, committed 18432K, reserved 1048576K
}
Event: 247.019 GC heap after
{Heap after GC invocations=75 (full 0):
 garbage-first heap   total 792576K, used 200574K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 8 young (16384K), 8 survivors (16384K)
 Metaspace       used 139637K, committed 140672K, reserved 1179648K
  class space    used 17944K, committed 18432K, reserved 1048576K
}
Event: 248.878 GC heap before
{Heap before GC invocations=75 (full 0):
 garbage-first heap   total 792576K, used 638846K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 199 young (407552K), 8 survivors (16384K)
 Metaspace       used 139869K, committed 140864K, reserved 1179648K
  class space    used 17953K, committed 18432K, reserved 1048576K
}
Event: 248.922 GC heap after
{Heap after GC invocations=76 (full 0):
 garbage-first heap   total 792576K, used 347838K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 29 young (59392K), 29 survivors (59392K)
 Metaspace       used 139869K, committed 140864K, reserved 1179648K
  class space    used 17953K, committed 18432K, reserved 1048576K
}
Event: 249.477 GC heap before
{Heap before GC invocations=77 (full 0):
 garbage-first heap   total 792576K, used 632510K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 158 young (323584K), 29 survivors (59392K)
 Metaspace       used 142285K, committed 143296K, reserved 1179648K
  class space    used 18159K, committed 18688K, reserved 1048576K
}
Event: 249.507 GC heap after
{Heap after GC invocations=78 (full 0):
 garbage-first heap   total 792576K, used 371224K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 20 young (40960K), 20 survivors (40960K)
 Metaspace       used 142285K, committed 143296K, reserved 1179648K
  class space    used 18159K, committed 18688K, reserved 1048576K
}
Event: 249.511 GC heap before
{Heap before GC invocations=78 (full 0):
 garbage-first heap   total 792576K, used 373272K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 21 young (43008K), 20 survivors (40960K)
 Metaspace       used 142285K, committed 143296K, reserved 1179648K
  class space    used 18159K, committed 18688K, reserved 1048576K
}
Event: 249.519 GC heap after
{Heap after GC invocations=79 (full 0):
 garbage-first heap   total 792576K, used 361150K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 3 young (6144K), 3 survivors (6144K)
 Metaspace       used 142285K, committed 143296K, reserved 1179648K
  class space    used 18159K, committed 18688K, reserved 1048576K
}
Event: 249.960 GC heap before
{Heap before GC invocations=79 (full 0):
 garbage-first heap   total 792576K, used 613054K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 127 young (260096K), 3 survivors (6144K)
 Metaspace       used 142961K, committed 143936K, reserved 1179648K
  class space    used 18199K, committed 18688K, reserved 1048576K
}
Event: 249.966 GC heap after
{Heap after GC invocations=80 (full 0):
 garbage-first heap   total 792576K, used 362338K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 4 young (8192K), 4 survivors (8192K)
 Metaspace       used 142961K, committed 143936K, reserved 1179648K
  class space    used 18199K, committed 18688K, reserved 1048576K
}
Event: 251.063 GC heap before
{Heap before GC invocations=80 (full 0):
 garbage-first heap   total 792576K, used 624482K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 132 young (270336K), 4 survivors (8192K)
 Metaspace       used 148097K, committed 149120K, reserved 1179648K
  class space    used 18861K, committed 19392K, reserved 1048576K
}
Event: 251.070 GC heap after
{Heap after GC invocations=81 (full 0):
 garbage-first heap   total 792576K, used 373232K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 8 young (16384K), 8 survivors (16384K)
 Metaspace       used 148097K, committed 149120K, reserved 1179648K
  class space    used 18861K, committed 19392K, reserved 1048576K
}
Event: 251.598 GC heap before
{Heap before GC invocations=81 (full 0):
 garbage-first heap   total 792576K, used 633328K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 136 young (278528K), 8 survivors (16384K)
 Metaspace       used 150170K, committed 151232K, reserved 1245184K
  class space    used 19100K, committed 19648K, reserved 1048576K
}
Event: 251.610 GC heap after
{Heap after GC invocations=82 (full 0):
 garbage-first heap   total 792576K, used 383941K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 14 young (28672K), 14 survivors (28672K)
 Metaspace       used 150170K, committed 151232K, reserved 1245184K
  class space    used 19100K, committed 19648K, reserved 1048576K
}
Event: 251.816 GC heap before
{Heap before GC invocations=82 (full 0):
 garbage-first heap   total 792576K, used 644037K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 141 young (288768K), 14 survivors (28672K)
 Metaspace       used 150481K, committed 151616K, reserved 1245184K
  class space    used 19145K, committed 19712K, reserved 1048576K
}
Event: 251.828 GC heap after
{Heap after GC invocations=83 (full 0):
 garbage-first heap   total 792576K, used 404202K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 18 young (36864K), 18 survivors (36864K)
 Metaspace       used 150481K, committed 151616K, reserved 1245184K
  class space    used 19145K, committed 19712K, reserved 1048576K
}
Event: 251.952 GC heap before
{Heap before GC invocations=83 (full 0):
 garbage-first heap   total 792576K, used 645866K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 136 young (278528K), 18 survivors (36864K)
 Metaspace       used 150500K, committed 151616K, reserved 1245184K
  class space    used 19146K, committed 19712K, reserved 1048576K
}
Event: 251.971 GC heap after
{Heap after GC invocations=84 (full 0):
 garbage-first heap   total 792576K, used 431323K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 16 young (32768K), 16 survivors (32768K)
 Metaspace       used 150500K, committed 151616K, reserved 1245184K
  class space    used 19146K, committed 19712K, reserved 1048576K
}
Event: 252.091 GC heap before
{Heap before GC invocations=84 (full 0):
 garbage-first heap   total 792576K, used 644315K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 121 young (247808K), 16 survivors (32768K)
 Metaspace       used 150521K, committed 151616K, reserved 1245184K
  class space    used 19148K, committed 19712K, reserved 1048576K
}

Deoptimization events (20 events):
Event: 252.076 Thread 0x000001a5029321b0 DEOPT PACKING pc=0x000001a525227b90 sp=0x00000062495f9310
Event: 252.076 Thread 0x000001a5029321b0 DEOPT UNPACKING pc=0x000001a5240423a3 sp=0x00000062495f93a0 mode 2
Event: 252.077 Thread 0x000001a5029321b0 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x000001a525249254 relative=0x0000000000000494
Event: 252.077 Thread 0x000001a5029321b0 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x000001a525249254 method=com.android.tools.r8.ir.regalloc.b.b(Lcom/android/tools/r8/ir/regalloc/c;)Z @ 26 c2
Event: 252.077 Thread 0x000001a5029321b0 DEOPT PACKING pc=0x000001a525249254 sp=0x00000062495f9070
Event: 252.077 Thread 0x000001a5029321b0 DEOPT UNPACKING pc=0x000001a5240423a3 sp=0x00000062495f9000 mode 2
Event: 252.077 Thread 0x000001a5029321b0 Uncommon trap: trap_request=0xffffff76 fr.pc=0x000001a5259381e0 relative=0x0000000000005280
Event: 252.077 Thread 0x000001a5029321b0 Uncommon trap: reason=predicate action=maybe_recompile pc=0x000001a5259381e0 method=com.android.tools.r8.ir.regalloc.c.a(Lcom/android/tools/r8/ir/regalloc/b;)V @ 71 c2
Event: 252.077 Thread 0x000001a5029321b0 DEOPT PACKING pc=0x000001a5259381e0 sp=0x00000062495f9060
Event: 252.077 Thread 0x000001a5029321b0 DEOPT UNPACKING pc=0x000001a5240423a3 sp=0x00000062495f8fc0 mode 2
Event: 252.077 Thread 0x000001a5029321b0 Uncommon trap: trap_request=0xffffff76 fr.pc=0x000001a5256c6e34 relative=0x0000000000001434
Event: 252.077 Thread 0x000001a5029321b0 Uncommon trap: reason=predicate action=maybe_recompile pc=0x000001a5256c6e34 method=com.android.tools.r8.ir.regalloc.c.a(Lcom/android/tools/r8/ir/regalloc/b;)V @ 71 c2
Event: 252.077 Thread 0x000001a5029321b0 DEOPT PACKING pc=0x000001a5256c6e34 sp=0x00000062495f8fe0
Event: 252.077 Thread 0x000001a5029321b0 DEOPT UNPACKING pc=0x000001a5240423a3 sp=0x00000062495f8fb8 mode 2
Event: 252.077 Thread 0x000001a502930860 DEOPT PACKING pc=0x000001a5207a5722 sp=0x00000062497f9370
Event: 252.077 Thread 0x000001a502930860 DEOPT UNPACKING pc=0x000001a524042b43 sp=0x00000062497f8ad0 mode 0
Event: 252.078 Thread 0x000001a5029321b0 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001a525c56834 relative=0x0000000000000d14
Event: 252.078 Thread 0x000001a5029321b0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001a525c56834 method=com.android.tools.r8.internal.v00.a(Lcom/android/tools/r8/internal/x4;)V @ 109 c2
Event: 252.078 Thread 0x000001a5029321b0 DEOPT PACKING pc=0x000001a525c56834 sp=0x00000062495f9730
Event: 252.078 Thread 0x000001a5029321b0 DEOPT UNPACKING pc=0x000001a5240423a3 sp=0x00000062495f9710 mode 2

Classes unloaded (20 events):
Event: 186.570 Thread 0x000001a53844dee0 Unloading class 0x000001a53aad5770 '_BuildScript_$_run_closure1$_closure7'
Event: 186.570 Thread 0x000001a53844dee0 Unloading class 0x000001a53aad5388 '_BuildScript_$_run_closure1$_closure6$_closure10'
Event: 186.570 Thread 0x000001a53844dee0 Unloading class 0x000001a53aad4fa0 '_BuildScript_$_run_closure1$_closure6$_closure9'
Event: 186.570 Thread 0x000001a53844dee0 Unloading class 0x000001a53aad4bb8 '_BuildScript_$_run_closure1$_closure6'
Event: 186.570 Thread 0x000001a53844dee0 Unloading class 0x000001a53aad47d0 '_BuildScript_$_run_closure1$_closure5'
Event: 186.570 Thread 0x000001a53844dee0 Unloading class 0x000001a53aad43e8 '_BuildScript_$_run_closure1$_closure4'
Event: 186.570 Thread 0x000001a53844dee0 Unloading class 0x000001a53aad4000 '_BuildScript_$_run_closure1'
Event: 186.570 Thread 0x000001a53844dee0 Unloading class 0x000001a53aad3800 '_BuildScript_'
Event: 186.570 Thread 0x000001a53844dee0 Unloading class 0x000001a53a9c3388 '_BuildScript_$_run_closure2'
Event: 186.570 Thread 0x000001a53844dee0 Unloading class 0x000001a53a9c2fa0 '_BuildScript_$_run_closure1$_closure7'
Event: 186.570 Thread 0x000001a53844dee0 Unloading class 0x000001a53a9c2bb8 '_BuildScript_$_run_closure1$_closure6'
Event: 186.570 Thread 0x000001a53844dee0 Unloading class 0x000001a53a9c27d0 '_BuildScript_$_run_closure1$_closure5$_closure9'
Event: 186.570 Thread 0x000001a53844dee0 Unloading class 0x000001a53a9c23e8 '_BuildScript_$_run_closure1$_closure5$_closure8'
Event: 186.570 Thread 0x000001a53844dee0 Unloading class 0x000001a53a9c2000 '_BuildScript_$_run_closure1$_closure5'
Event: 186.570 Thread 0x000001a53844dee0 Unloading class 0x000001a53a9c0c00 '_BuildScript_$_run_closure1$_closure4'
Event: 186.570 Thread 0x000001a53844dee0 Unloading class 0x000001a53a9c1920 '_BuildScript_$_run_closure1$_closure3'
Event: 186.570 Thread 0x000001a53844dee0 Unloading class 0x000001a53a9c1538 '_BuildScript_$_run_closure1'
Event: 186.570 Thread 0x000001a53844dee0 Unloading class 0x000001a53a9c1000 '_BuildScript_'
Event: 248.999 Thread 0x000001a53844dee0 Unloading class 0x000001a53b0d9c00 'java/lang/invoke/LambdaForm$DMH+0x000001a53b0d9c00'
Event: 248.999 Thread 0x000001a53844dee0 Unloading class 0x000001a53b06d000 'java/lang/invoke/LambdaForm$DMH+0x000001a53b06d000'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 251.526 Thread 0x000001a57a3f2fb0 Exception <a 'sun/nio/fs/WindowsException'{0x0000000722dc51f8}> (0x0000000722dc51f8) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]
Event: 251.526 Thread 0x000001a57a3f2fb0 Exception <a 'sun/nio/fs/WindowsException'{0x0000000722dc7020}> (0x0000000722dc7020) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]
Event: 251.531 Thread 0x000001a57a3f2fb0 Exception <a 'sun/nio/fs/WindowsException'{0x0000000722dd88e0}> (0x0000000722dd88e0) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]
Event: 251.534 Thread 0x000001a57a3f2fb0 Exception <a 'sun/nio/fs/WindowsException'{0x00000007229c2868}> (0x00000007229c2868) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]
Event: 251.534 Thread 0x000001a57a3f2fb0 Exception <a 'sun/nio/fs/WindowsException'{0x00000007229c3750}> (0x00000007229c3750) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]
Event: 251.534 Thread 0x000001a57a3f2fb0 Exception <a 'sun/nio/fs/WindowsException'{0x00000007229c37e0}> (0x00000007229c37e0) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]
Event: 251.534 Thread 0x000001a57a3f2fb0 Exception <a 'sun/nio/fs/WindowsException'{0x00000007229c3c90}> (0x00000007229c3c90) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]
Event: 251.750 Thread 0x000001a57a3edeb0 Exception <a 'sun/nio/fs/WindowsException'{0x0000000728fde778}> (0x0000000728fde778) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]
Event: 251.778 Thread 0x000001a57f4b92c0 Exception <a 'sun/nio/fs/WindowsException'{0x0000000726922d18}> (0x0000000726922d18) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]
Event: 251.779 Thread 0x000001a57f4bb630 Exception <a 'sun/nio/fs/WindowsException'{0x00000007262bce88}> (0x00000007262bce88) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]
Event: 251.780 Thread 0x000001a5002d6c60 Exception <a 'sun/nio/fs/WindowsException'{0x000000072617ced0}> (0x000000072617ced0) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]
Event: 251.782 Thread 0x000001a5016841d0 Exception <a 'sun/nio/fs/WindowsException'{0x0000000725a26f60}> (0x0000000725a26f60) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]
Event: 251.788 Thread 0x000001a502934010 Exception <a 'sun/nio/fs/WindowsException'{0x000000072462bd00}> (0x000000072462bd00) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]
Event: 251.795 Thread 0x000001a502930860 Exception <a 'sun/nio/fs/WindowsException'{0x0000000723313180}> (0x0000000723313180) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]
Event: 251.802 Thread 0x000001a5029335f0 Exception <a 'sun/nio/fs/WindowsException'{0x00000007220b7ea8}> (0x00000007220b7ea8) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]
Event: 251.802 Thread 0x000001a5029321b0 Exception <a 'sun/nio/fs/WindowsException'{0x00000007220dc1f0}> (0x00000007220dc1f0) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]
Event: 251.834 Thread 0x000001a57f4bbb40 Exception <a 'sun/nio/fs/WindowsException'{0x000000072f695af0}> (0x000000072f695af0) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]
Event: 251.851 Thread 0x000001a57a3edeb0 Exception <a 'sun/nio/fs/WindowsException'{0x000000072d411c98}> (0x000000072d411c98) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]
Event: 251.917 Thread 0x000001a5016841d0 Exception <a 'sun/nio/fs/WindowsException'{0x00000007250313a0}> (0x00000007250313a0) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]
Event: 251.978 Thread 0x000001a57f4b92c0 Exception <a 'sun/nio/fs/WindowsException'{0x000000072f821d68}> (0x000000072f821d68) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]

VM Operations (20 events):
Event: 251.480 Executing VM operation: ICBufferFull done
Event: 251.509 Executing VM operation: HandshakeAllThreads
Event: 251.509 Executing VM operation: HandshakeAllThreads done
Event: 251.598 Executing VM operation: G1CollectForAllocation
Event: 251.598 Executing VM operation: G1CollectForAllocation done
Event: 251.598 Executing VM operation: G1CollectForAllocation
Event: 251.610 Executing VM operation: G1CollectForAllocation done
Event: 251.659 Executing VM operation: HandshakeAllThreads
Event: 251.659 Executing VM operation: HandshakeAllThreads done
Event: 251.816 Executing VM operation: G1CollectForAllocation
Event: 251.829 Executing VM operation: G1CollectForAllocation done
Event: 251.948 Executing VM operation: G1CollectForAllocation
Event: 251.951 Executing VM operation: G1CollectForAllocation done
Event: 251.951 Executing VM operation: G1CollectForAllocation
Event: 251.971 Executing VM operation: G1CollectForAllocation done
Event: 252.068 Executing VM operation: HandshakeAllThreads
Event: 252.071 Executing VM operation: HandshakeAllThreads done
Event: 252.090 Executing VM operation: G1CollectForAllocation
Event: 252.090 Executing VM operation: G1CollectForAllocation done
Event: 252.091 Executing VM operation: G1CollectForAllocation

Events (20 events):
Event: 251.500 loading class sun/reflect/misc/MethodUtil done
Event: 251.500 loading class sun/reflect/misc/MethodUtil$1
Event: 251.500 loading class sun/reflect/misc/MethodUtil$1 done
Event: 251.538 loading class jdk/internal/reflect/UnsafeQualifiedBooleanFieldAccessorImpl
Event: 251.538 loading class jdk/internal/reflect/UnsafeQualifiedBooleanFieldAccessorImpl done
Event: 251.621 loading class java/util/stream/ReferencePipeline$15
Event: 251.622 loading class java/util/stream/ReferencePipeline$15 done
Event: 251.622 loading class java/util/stream/ReferencePipeline$15$1
Event: 251.622 loading class java/util/stream/ReferencePipeline$15$1 done
Event: 251.674 loading class java/util/TreeMap$AscendingSubMap
Event: 251.675 loading class java/util/TreeMap$AscendingSubMap done
Event: 251.675 loading class java/util/TreeMap$NavigableSubMap$SubMapKeyIterator
Event: 251.675 loading class java/util/TreeMap$NavigableSubMap$SubMapKeyIterator done
Event: 251.730 loading class java/util/zip/ZipOutputStream$XEntry
Event: 251.730 loading class java/util/zip/ZipOutputStream$XEntry done
Event: 252.076 Thread 0x000001a538472910 flushing nmethod 0x000001a524ee7590
Event: 252.078 Thread 0x000001a538472910 flushing nmethod 0x000001a52511c990
Event: 252.080 Thread 0x000001a538472910 flushing nmethod 0x000001a525572e10
Event: 252.087 Thread 0x000001a538472910 flushing nmethod 0x000001a525f06510
Event: 252.087 Thread 0x000001a538472910 flushing nmethod 0x000001a525f91f10


Dynamic libraries:
0x00007ff718a50000 - 0x00007ff718a60000 	F:\Program Files\Java\jdk-17\bin\java.exe
0x00007ffb035a0000 - 0x00007ffb03803000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffb027a0000 - 0x00007ffb02867000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffb00980000 - 0x00007ffb00d49000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffb00830000 - 0x00007ffb0097c000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffae37f0000 - 0x00007ffae3809000 	F:\Program Files\Java\jdk-17\bin\jli.dll
0x00007ffae5db0000 - 0x00007ffae5dcb000 	F:\Program Files\Java\jdk-17\bin\VCRUNTIME140.dll
0x00007ffb033d0000 - 0x00007ffb03482000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffb02050000 - 0x00007ffb020f9000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffb02690000 - 0x00007ffb02736000 	C:\WINDOWS\System32\sechost.dll
0x00007ffb02870000 - 0x00007ffb02986000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffb024c0000 - 0x00007ffb0268a000 	C:\WINDOWS\System32\USER32.dll
0x00007ffb01170000 - 0x00007ffb01197000 	C:\WINDOWS\System32\win32u.dll
0x00007ffb01f20000 - 0x00007ffb01f4a000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffb01030000 - 0x00007ffb01161000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffb00d50000 - 0x00007ffb00df3000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffadfe20000 - 0x00007ffae00b0000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.3323_none_3e088096e3344490\COMCTL32.dll
0x00007ffafa4a0000 - 0x00007ffafa4ab000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffb03490000 - 0x00007ffb034bf000 	C:\WINDOWS\System32\IMM32.DLL
0x0000000071a90000 - 0x0000000071a9d000 	F:\Program Files (x86)\360\360Safe\safemon\SafeWrapper.dll
0x00007ffaf1d50000 - 0x00007ffaf1dfe000 	F:\Program Files (x86)\360\360Safe\safemon\libzdtp64.dll
0x00007ffb02b80000 - 0x00007ffb03295000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffb00ec0000 - 0x00007ffb01028000 	C:\WINDOWS\System32\wintypes.dll
0x00007ffb013c0000 - 0x00007ffb01742000 	C:\WINDOWS\System32\combase.dll
0x00007ffb02460000 - 0x00007ffb024bd000 	C:\WINDOWS\System32\SHLWAPI.dll
0x00007ffaf3160000 - 0x00007ffaf316c000 	F:\Program Files\Java\jdk-17\bin\vcruntime140_1.dll
0x00007ffab2c70000 - 0x00007ffab2cfe000 	F:\Program Files\Java\jdk-17\bin\msvcp140.dll
0x00007ffa24cd0000 - 0x00007ffa258b0000 	F:\Program Files\Java\jdk-17\bin\server\jvm.dll
0x00007ffb02a90000 - 0x00007ffb02a98000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffafa460000 - 0x00007ffafa496000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffab1fc0000 - 0x00007ffab1fca000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007ffb02990000 - 0x00007ffb02a04000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffaff550000 - 0x00007ffaff56a000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffaf30b0000 - 0x00007ffaf30ba000 	F:\Program Files\Java\jdk-17\bin\jimage.dll
0x00007ffafdba0000 - 0x00007ffafdde1000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffb02aa0000 - 0x00007ffb02b76000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffaf2330000 - 0x00007ffaf2369000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffb011a0000 - 0x00007ffb01239000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffacd9e0000 - 0x00007ffacd9ee000 	F:\Program Files\Java\jdk-17\bin\instrument.dll
0x00007ffad10e0000 - 0x00007ffad1105000 	F:\Program Files\Java\jdk-17\bin\java.dll
0x00007ffab2b90000 - 0x00007ffab2c67000 	F:\Program Files\Java\jdk-17\bin\jsvml.dll
0x00007ffafe430000 - 0x00007ffafec75000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffb01f50000 - 0x00007ffb0203b000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffb006b0000 - 0x00007ffb006df000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffab38d0000 - 0x00007ffab38e8000 	F:\Program Files\Java\jdk-17\bin\zip.dll
0x00007ffab5fe0000 - 0x00007ffab5ff9000 	F:\Program Files\Java\jdk-17\bin\net.dll
0x00007ffaf7ac0000 - 0x00007ffaf7bde000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffaffa50000 - 0x00007ffaffaba000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffab3920000 - 0x00007ffab3936000 	F:\Program Files\Java\jdk-17\bin\nio.dll
0x00007ffaf2850000 - 0x00007ffaf2860000 	F:\Program Files\Java\jdk-17\bin\verify.dll
0x00007ffad1200000 - 0x00007ffad1227000 	H:\Users\zhang\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64\native-platform.dll
0x00007ffaacb10000 - 0x00007ffaacc54000 	H:\Users\zhang\.gradle\native\38dada09dfb8b06ba9b0570ebf7e218e3eb74d4ef43ca46872605cf95ebc2f47\windows-amd64\native-platform-file-events.dll
0x00007ffad0ff0000 - 0x00007ffad0ffa000 	F:\Program Files\Java\jdk-17\bin\management.dll
0x00007ffad0fd0000 - 0x00007ffad0fdb000 	F:\Program Files\Java\jdk-17\bin\management_ext.dll
0x00007ffaffe00000 - 0x00007ffaffe1c000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffaff4b0000 - 0x00007ffaff4ea000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffaffaf0000 - 0x00007ffaffb1b000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffafffc0000 - 0x00007ffafffe6000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007ffaffc80000 - 0x00007ffaffc8c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffafefc0000 - 0x00007ffafeff3000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffb02040000 - 0x00007ffb0204a000 	C:\WINDOWS\System32\NSI.dll
0x00007ffaf7a90000 - 0x00007ffaf7aaf000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ffaf7a60000 - 0x00007ffaf7a85000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007ffaff000000 - 0x00007ffaff125000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007ffadf3f0000 - 0x00007ffadf3fe000 	F:\Program Files\Java\jdk-17\bin\sunmscapi.dll
0x00007ffb01240000 - 0x00007ffb013b7000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ffafff80000 - 0x00007ffafffb0000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007ffaffef0000 - 0x00007ffafff2f000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007ffae32a0000 - 0x00007ffae32a8000 	C:\WINDOWS\system32\wshunix.dll
0x00007ffaf4eb0000 - 0x00007ffaf4ebb000 	C:\Windows\System32\rasadhlp.dll
0x00007ffaf6c50000 - 0x00007ffaf6cd5000 	C:\WINDOWS\System32\fwpuclnt.dll
0x00007ffaff670000 - 0x00007ffaff6a5000 	C:\WINDOWS\SYSTEM32\ntmarta.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;F:\Program Files\Java\jdk-17\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.3323_none_3e088096e3344490;F:\Program Files (x86)\360\360Safe\safemon;F:\Program Files\Java\jdk-17\bin\server;H:\Users\zhang\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64;H:\Users\zhang\.gradle\native\38dada09dfb8b06ba9b0570ebf7e218e3eb74d4ef43ca46872605cf95ebc2f47\windows-amd64

VM Arguments:
jvm_args: -XX:+HeapDumpOnOutOfMemoryError -XX:+UseG1GC --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx4096m -Dfile.encoding=UTF-8 -Duser.country=CN -Duser.language=zh -Duser.variant -javaagent:H:\Users\zhang\.gradle\wrapper\dists\gradle-8.5-bin\5hry6tgzq0wontdz18qo6fdj9\gradle-8.5\lib\agents\gradle-instrumentation-agent-8.5.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.5
java_class_path (initial): H:\Users\zhang\.gradle\wrapper\dists\gradle-8.5-bin\5hry6tgzq0wontdz18qo6fdj9\gradle-8.5\lib\gradle-launcher-8.5.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
   size_t InitialHeapSize                          = 526385152                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4294967296                                {product} {command line}
   size_t MaxNewSize                               = 2575302656                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4294967296                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {command line}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=F:\Program Files\Java\jdk-17
CLASSPATH=P:\tmp\AndroidDialer-master (2)\AndroidDialer-master\\gradle\wrapper\gradle-wrapper.jar
PATH=F:\ProgramData\miniconda3;F:\ProgramData\miniconda3\Library\mingw-w64\bin;F:\ProgramData\miniconda3\Library\usr\bin;F:\ProgramData\miniconda3\Library\bin;F:\ProgramData\miniconda3\Scripts;F:\ProgramData\miniconda3\bin;F:\ProgramData\miniconda3\condabin;C:\Program Files\PowerShell\7;I:\Program Files\python\python12\Scripts;I:\Program Files\python\python12;F:\Program Files\ShadowBot;F:\Program Files\Git\bin;C:\Program Files\Common Files\Oracle\Java\javapath;F:\Program Files (x86)\ShadowBot;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files\Microsoft SQL Server\150\Tools\Binn;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn;C:\Program Files\dotnet;F:\Windows Kits\10\Windows Performance Toolkit;H:\Android\Sdk\platform-tools;F:\Program Files\Java\jdk-17\bin;F:\Program Files\Docker\resources\bin;F:\ProgramData\miniconda3\Scripts;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\OpenSSH;C:\Program Files\PowerShell\7;H:\Users\zhang\AppData\Local\Programs\Python\Python312;F:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin;H:\Users\zhang\AppData\Roaming\Python\Python312\Scripts;C:\Users\<USER>\AppData\Local\Programs\Python\Python37\Scripts;C:\Users\<USER>\AppData\Local\Programs\Python\Python37;O:\C++tools\vcpkg-master\vcpkg-master;F:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.39.33519\bin\Hostx64\x64;G:\ffmpeg\ffmpeg-2025-05-21-git-4099d53759-essentials_build\ffmpeg-2025-05-21-git-4099d53759-essentials_build\bin;H:\Users\zhang\AppData\Roaming\npm;F:\Program Files\nodejs;F:\Users\zhang\AppData\Local\Programs\Tesseract-OCR;C:\ProgramData\chocolatey\bin;C:\Program Files\gs\gs10.05.1\bin;O:\php\php-8.4.8;C:\Program Files (x86)\NetSarang\Xftp 8;H:\Users\zhang\AppData\Local\Programs\Python\Launcher;C:\Program Files\Go
USERNAME=zhang
LANG=zh_CN.UTF-8
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 25 Model 80 Stepping 0, AuthenticAMD



---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.3323)
OS uptime: 5 days 7:32 hours
Hyper-V role detected

CPU: total 12 (initial active 12) (12 cores per cpu, 2 threads per core) family 25 model 80 stepping 0 microcode 0xa50000f, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, hv

Memory: 4k page, system-wide physical 32051M (975M free)
TotalPageFile size 47369M (AvailPageFile size 912M)
current process WorkingSet (physical memory assigned to process): 1286M, peak: 1297M
current process commit charge ("private bytes"): 1338M, peak: 2334M

vm_info: Java HotSpot(TM) 64-Bit Server VM (17.0.11+7-LTS-207) for windows-amd64 JRE (17.0.11+7-LTS-207), built on Mar 11 2024 19:01:50 by "mach5one" with MS VC++ 17.6 (VS2022)

END.
