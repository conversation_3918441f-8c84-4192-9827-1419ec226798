# AndroidX Migration Script
$sourceDir = "com.android.dialer\src\main\java"

# Define the mapping from support library to AndroidX
$replacements = @{
    'android.support.annotation.Nullable' = 'androidx.annotation.Nullable'
    'android.support.annotation.NonNull' = 'androidx.annotation.NonNull'
    'android.support.v4.content.ContextCompat' = 'androidx.core.content.ContextCompat'
    'android.support.v4.content.FileProvider' = 'androidx.core.content.FileProvider'
    'android.support.v4.util.Pair' = 'androidx.core.util.Pair'
    'android.support.v4.os.BuildCompat' = 'androidx.core.os.BuildCompat'
    'android.support.v4.graphics.drawable.RoundedBitmapDrawable' = 'androidx.core.graphics.drawable.RoundedBitmapDrawable'
    'android.support.v4.graphics.drawable.RoundedBitmapDrawableFactory' = 'androidx.core.graphics.drawable.RoundedBitmapDrawableFactory'
    'android.support.v7.app.ActionBar' = 'androidx.appcompat.app.ActionBar'
    'android.support.v7.widget.RecyclerView' = 'androidx.recyclerview.widget.RecyclerView'
    'android.support.v7.widget.LinearLayoutManager' = 'androidx.recyclerview.widget.LinearLayoutManager'
    'android.support.v7.widget.CardView' = 'androidx.cardview.widget.CardView'
    'android.support.v13.app.FragmentPagerAdapter' = 'androidx.fragment.app.FragmentPagerAdapter'
    'android.support.v4.view.ViewPager' = 'androidx.viewpager.widget.ViewPager'
    'android.support.design.widget.Snackbar' = 'com.google.android.material.snackbar.Snackbar'
}

# Get all Java files recursively
$javaFiles = Get-ChildItem -Path $sourceDir -Filter "*.java" -Recurse

foreach ($file in $javaFiles) {
    $content = Get-Content $file.FullName -Raw
    $modified = $false
    
    foreach ($oldImport in $replacements.Keys) {
        $newImport = $replacements[$oldImport]
        if ($content -match [regex]::Escape("import $oldImport;")) {
            $content = $content -replace [regex]::Escape("import $oldImport;"), "import $newImport;"
            $modified = $true
            Write-Host "Replaced $oldImport with $newImport in $($file.Name)"
        }
    }
    
    if ($modified) {
        Set-Content -Path $file.FullName -Value $content -NoNewline
    }
}

Write-Host "Migration completed!"