@echo off
echo Android项目构建问题诊断脚本
echo ================================

echo 步骤1: 检查Java版本
java -version
echo.

echo 步骤2: 检查Gradle版本
call gradlew --version
echo.

echo 步骤3: 停止所有Gradle守护进程
call gradlew --stop
echo.

echo 步骤4: 清理项目
call gradlew clean
echo.

echo 步骤5: 尝试构建各个模块（带详细日志）
echo 构建 com.android.common...
call gradlew :com.android.common:assembleDebug --stacktrace --info
if %errorlevel% neq 0 (
    echo com.android.common 构建失败！
    pause
    exit /b 1
)

echo 构建 com.android.phone.common...
call gradlew :com.android.phone.common:assembleDebug --stacktrace --info
if %errorlevel% neq 0 (
    echo com.android.phone.common 构建失败！
    pause
    exit /b 1
)

echo 构建 com.android.contacts.common...
call gradlew :com.android.contacts.common:assembleDebug --stacktrace --info
if %errorlevel% neq 0 (
    echo com.android.contacts.common 构建失败！
    pause
    exit /b 1
)

echo 构建 com.android.dialer...
call gradlew :com.android.dialer:assembleDebug --stacktrace --info
if %errorlevel% neq 0 (
    echo com.android.dialer 构建失败！
    pause
    exit /b 1
)

echo.
echo 所有模块构建成功！
pause